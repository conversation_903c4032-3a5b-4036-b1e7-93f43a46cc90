"""
Alpaca API client for market data and trading
"""
import alpaca_trade_api as tradeapi
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Any
import time
from config import config

class AlpacaClient:
    """Alpaca API client for data and trading operations"""
    
    def __init__(self):
        """Initialize Alpaca client with API credentials"""
        self.logger = logging.getLogger(__name__)
        
        # Get credentials from config
        self.api_key = config.get("alpaca.api_key")
        self.secret_key = config.get("alpaca.secret_key")
        self.base_url = config.get("alpaca.base_url")
        
        if not self.api_key or not self.secret_key:
            self.logger.error("Alpaca API credentials not configured")
            self.api = None
            return
        
        try:
            # Initialize Alpaca API
            self.api = tradeapi.REST(
                key_id=self.api_key,
                secret_key=self.secret_key,
                base_url=self.base_url,
                api_version='v2'
            )
            
            # Test connection
            account = self.api.get_account()
            self.logger.info(f"Connected to Alpaca. Account status: {account.status}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Alpaca client: {e}")
            self.api = None
    
    def is_connected(self) -> bool:
        """Check if client is properly connected"""
        return self.api is not None
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get account information"""
        if not self.is_connected():
            return None
        
        try:
            account = self.api.get_account()
            return {
                'buying_power': float(account.buying_power),
                'cash': float(account.cash),
                'portfolio_value': float(account.portfolio_value),
                'equity': float(account.equity),
                'day_trade_count': getattr(account, 'day_trade_count', 0),
                'pattern_day_trader': getattr(account, 'pattern_day_trader', False)
            }
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return None
    
    def get_historical_data(self, 
                          symbol: str, 
                          timeframe: str = '5Min',
                          limit: int = 1000,
                          start: Optional[datetime] = None,
                          end: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        Get historical market data for a symbol
        
        Args:
            symbol: Stock symbol
            timeframe: Data timeframe (1Min, 5Min, 15Min, 1Hour, 1Day)
            limit: Maximum number of bars to retrieve
            start: Start date for data
            end: End date for data
            
        Returns:
            DataFrame with OHLCV data
        """
        if not self.is_connected():
            self.logger.error("Alpaca client not connected")
            return None
        
        try:
            # Set default date range if not provided
            if not end:
                end = datetime.now()
            if not start:
                start = end - timedelta(days=30)  # Default to 30 days
            
            # Get bars from Alpaca
            bars = self.api.get_bars(
                symbol,
                timeframe,
                start=start.strftime('%Y-%m-%d'),
                end=end.strftime('%Y-%m-%d'),
                limit=limit,
                adjustment='raw'
            ).df
            
            if bars.empty:
                self.logger.warning(f"No data returned for {symbol}")
                return None
            
            # Rename columns to match expected format
            bars = bars.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            })
            
            # Ensure we have the required columns
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            for col in required_cols:
                if col not in bars.columns:
                    self.logger.error(f"Missing column {col} in data for {symbol}")
                    return None
            
            self.logger.debug(f"Retrieved {len(bars)} bars for {symbol}")
            return bars
            
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return None
    
    def get_multiple_symbols_data(self, 
                                 symbols: List[str], 
                                 timeframe: str = '5Min',
                                 limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """
        Get historical data for multiple symbols
        
        Args:
            symbols: List of stock symbols
            timeframe: Data timeframe
            limit: Maximum number of bars per symbol
            
        Returns:
            Dictionary with symbol as key and DataFrame as value
        """
        data_dict = {}
        
        for symbol in symbols:
            try:
                df = self.get_historical_data(symbol, timeframe, limit)
                if df is not None and not df.empty:
                    data_dict[symbol] = df
                else:
                    self.logger.warning(f"No data available for {symbol}")
                
                # Add small delay to avoid rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error getting data for {symbol}: {e}")
                continue
        
        self.logger.info(f"Retrieved data for {len(data_dict)} out of {len(symbols)} symbols")
        return data_dict
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        if not self.is_connected():
            return None
        
        try:
            quote = self.api.get_latest_quote(symbol)
            return float(quote.bid_price + quote.ask_price) / 2  # Mid price
        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    def place_bracket_order(self, 
                           symbol: str,
                           qty: int,
                           side: str,
                           stop_loss_price: float,
                           take_profit_price: float) -> Optional[str]:
        """
        Place a bracket order (entry + stop loss + take profit)
        
        Args:
            symbol: Stock symbol
            qty: Quantity to trade
            side: 'buy' or 'sell'
            stop_loss_price: Stop loss price
            take_profit_price: Take profit price
            
        Returns:
            Order ID if successful, None otherwise
        """
        if not self.is_connected():
            self.logger.error("Cannot place order - Alpaca client not connected")
            return None
        
        try:
            order = self.api.submit_order(
                symbol=symbol,
                qty=qty,
                side=side,
                type='market',
                time_in_force='day',
                order_class='bracket',
                stop_loss={'stop_price': stop_loss_price},
                take_profit={'limit_price': take_profit_price}
            )
            
            self.logger.info(f"Bracket order placed for {symbol}: {order.id}")
            return order.id
            
        except Exception as e:
            self.logger.error(f"Error placing bracket order for {symbol}: {e}")
            return None
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions"""
        if not self.is_connected():
            return []
        
        try:
            positions = self.api.list_positions()
            position_list = []
            
            for pos in positions:
                position_list.append({
                    'symbol': pos.symbol,
                    'qty': float(pos.qty),
                    'side': pos.side,
                    'market_value': float(pos.market_value),
                    'cost_basis': float(pos.cost_basis),
                    'unrealized_pl': float(pos.unrealized_pl),
                    'unrealized_plpc': float(pos.unrealized_plpc),
                    'avg_entry_price': float(pos.avg_entry_price),
                    'current_price': float(pos.current_price)
                })
            
            return position_list
            
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []
    
    def get_orders(self, status: str = 'open') -> List[Dict[str, Any]]:
        """Get orders by status"""
        if not self.is_connected():
            return []
        
        try:
            orders = self.api.list_orders(status=status)
            order_list = []
            
            for order in orders:
                order_list.append({
                    'id': order.id,
                    'symbol': order.symbol,
                    'qty': float(order.qty),
                    'side': order.side,
                    'type': order.type,
                    'status': order.status,
                    'filled_qty': float(order.filled_qty or 0),
                    'limit_price': float(order.limit_price) if order.limit_price else None,
                    'stop_price': float(order.stop_price) if order.stop_price else None,
                    'submitted_at': order.submitted_at
                })
            
            return order_list
            
        except Exception as e:
            self.logger.error(f"Error getting orders: {e}")
            return []
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        if not self.is_connected():
            return False
        
        try:
            self.api.cancel_order(order_id)
            self.logger.info(f"Order {order_id} cancelled")
            return True
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        if not self.is_connected():
            return False
        
        try:
            clock = self.api.get_clock()
            return clock.is_open
        except Exception as e:
            self.logger.error(f"Error checking market status: {e}")
            return False
