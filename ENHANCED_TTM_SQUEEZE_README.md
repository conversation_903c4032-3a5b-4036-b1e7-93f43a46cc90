# Enhanced TTM Squeeze Detection Algorithm

## Overview

This enhanced TTM Squeeze implementation provides **early breakout signal detection** by identifying momentum transitions during the critical "yellow phase" - when momentum is still negative but beginning to rise. This provides traders with earlier entry opportunities compared to traditional implementations that wait for positive momentum.

## Key Enhancements

### 1. **Red Histogram Phase Detection**
- Identifies 4-5 consecutive bars where momentum is negative AND decreasing
- Each bar must be lower than the previous (downward momentum trend)
- Represents the compression phase building up energy for potential breakout

### 2. **Yellow Transition Phase Detection**  
- Detects 2+ consecutive bars where momentum is still negative BUT rising
- Momentum values are higher than previous red bars but still below zero
- This is the critical early warning phase before momentum turns positive

### 3. **Enhanced Early Signal Generation**
- Triggers during the yellow phase (proactive vs reactive)
- Combines multiple confirmation factors:
  - Active squeeze for 6+ bars
  - Current yellow phase detection
  - Momentum acceleration analysis
  - Volume confirmation (when available)
  - Recent red phase history

## Algorithm Components

### Core TTM Squeeze Elements
```python
# Bollinger Bands (20-period, 2.0 std dev)
upper_BB = SMA + (2.0 * StdDev)
lower_BB = SMA - (2.0 * StdDev)

# Keltner Channels (20-period, 1.5 ATR multiplier)
upper_KC = SMA + (1.5 * ATR)
lower_KC = SMA - (1.5 * ATR)

# Squeeze Condition
squeeze_on = (lower_BB > lower_KC) AND (upper_BB < upper_KC)

# Momentum (Linear Regression Slope of 20-period close prices)
momentum = linreg_slope(close, 20)
```

### Enhanced Phase Detection

#### Red Phase Criteria:
- `momentum < 0` (negative)
- `momentum_change < 0` (decreasing)
- `consecutive_count >= 4` (sustained trend)

#### Yellow Phase Criteria:
- `momentum < 0` (still negative)
- `momentum_change > 0` (rising)
- `consecutive_count >= 2` (confirmed trend)

#### Enhanced Signal Conditions:
1. **Base Requirements:**
   - Squeeze active for 6+ bars
   - Currently in yellow phase
   - Momentum still negative but rising for 2+ bars

2. **Additional Confirmations:**
   - Momentum strength above 10-period average
   - Positive momentum acceleration
   - Volume 20% above 20-period average (if available)
   - Recent red phase within last 8 bars

## Signal Types

### 1. **Enhanced Early Signal** (Primary)
- Triggers during yellow phase transition
- Highest priority signal with multiple confirmations
- Provides earliest entry opportunity

### 2. **Regular Early Signal** (Secondary)
- Original early detection logic
- Backup signal for edge cases

### 3. **Combined TTM Signal**
- Union of enhanced and regular signals
- Comprehensive signal coverage

## Usage Example

```python
from ttm_squeeze import TTMSqueezeDetector

# Initialize detector
detector = TTMSqueezeDetector(
    bb_length=20,           # Bollinger Bands period
    bb_multiplier=2.0,      # BB standard deviation multiplier
    kc_length=20,           # Keltner Channel period  
    kc_multiplier=1.5,      # KC ATR multiplier
    min_squeeze_bars=6,     # Minimum squeeze duration
    momentum_length=20      # Momentum calculation period
)

# Calculate indicators and signals
df_with_signals = detector.calculate_indicators(df)

# Get current signal status
signal_info = detector.get_current_signal(df_with_signals)

print(f"Enhanced Early Signal: {signal_info['enhanced_early_signal']}")
print(f"Yellow Phase: {signal_info['yellow_phase']}")
print(f"Red Phase: {signal_info['red_phase']}")
print(f"Momentum: {signal_info['momentum']:.4f}")
```

## Signal Information Output

```python
{
    'has_signal': bool,              # Combined signal status
    'early_signal': bool,            # Regular early signal
    'enhanced_early_signal': bool,   # Enhanced early signal
    'squeeze_active': bool,          # Current squeeze status
    'squeeze_count': int,            # Consecutive squeeze bars
    'momentum': float,               # Current momentum value
    'momentum_strength': float,      # Absolute momentum value
    'red_phase': bool,              # In red histogram phase
    'yellow_phase': bool,           # In yellow transition phase
    'current_price': float,         # Latest close price
    'squeeze_strength': float,      # Squeeze tightness measure
    'timestamp': datetime           # Signal timestamp
}
```

## Trading Strategy Integration

### Entry Signals
- **Primary**: Enhanced early signal during yellow phase
- **Confirmation**: Volume above average, momentum acceleration
- **Direction**: Determined by momentum sign when it turns positive

### Risk Management
- **Stop Loss**: Below/above recent squeeze low/high
- **Position Size**: Based on ATR and squeeze strength
- **Exit**: When momentum shows signs of exhaustion

### Advantages Over Traditional TTM Squeeze
1. **Earlier Entry**: Signals before momentum turns positive
2. **Better Risk/Reward**: Enter closer to breakout initiation
3. **Reduced Slippage**: Avoid late entries after momentum confirmation
4. **Higher Probability**: Multiple confirmation factors reduce false signals

## Testing and Validation

Run the test script to see the enhanced algorithm in action:

```bash
python test_enhanced_ttm_squeeze.py
```

This will:
- Test with sample data showing squeeze patterns
- Analyze real market data for multiple symbols
- Generate comprehensive visualization plots
- Display signal analysis and timing comparisons

## Visualization Features

The test script creates three-panel charts showing:

1. **Price Chart**: Bollinger Bands, Keltner Channels, squeeze periods, and signal markers
2. **Momentum Histogram**: Color-coded phases (red, yellow, green) with signal timing
3. **Squeeze Analysis**: Duration tracking and momentum strength indicators

## Performance Considerations

- Requires minimum 20 bars for momentum calculation
- Enhanced analysis needs 15+ bars for reliable signals
- Real-time performance optimized for streaming data
- Memory efficient with rolling calculations

## Quick Start Guide

### 1. Launch the Application
```bash
# Run the enhanced trading launcher
python enhanced_ttm_trading.py

# Or run specific components
python test_enhanced_ttm_squeeze.py  # Console interface
python main.py                       # GUI application
```

### 2. Application Modes

**GUI Mode (Recommended)**
- Full desktop interface with multiple tabs
- Enhanced signals tab with real-time scanning
- Manual trading interface with custom parameters
- Trailing stops management
- Performance tracking

**Console Mode**
- Text-based trading interface
- Clean signal display without charts
- Interactive manual trading
- Perfect for automated/scripted trading

**Test Mode**
- Quick signal testing on sample stocks
- Validation of enhanced detection algorithm
- No actual trading - analysis only

### 3. Trading Workflow

1. **Scan for Signals**: Use enhanced scanner to find TTM Squeeze setups
2. **Analyze Signals**: Review momentum phases and signal strength
3. **Manual Trading**: Select signals and customize trade parameters
4. **Risk Management**: Set stop-loss, take-profit, and trailing stops
5. **Monitor Positions**: Track active trades and performance

## Integration with Existing System

The enhanced TTM Squeeze seamlessly integrates with your existing trading framework:

- **Alpaca Integration**: Uses your existing API credentials and connection
- **Trade Manager**: Compatible with existing position tracking
- **GUI Framework**: Extends current tkinter interface
- **Configuration**: Uses existing config.json settings
- **Logging**: Integrates with current logging system

## Key Files

```
enhanced_ttm_squeeze/
├── ttm_squeeze.py              # Enhanced TTM Squeeze detector
├── test_enhanced_ttm_squeeze.py # Console trading interface
├── trailing_stop_manager.py    # Trailing stop functionality
├── enhanced_ttm_trading.py     # Application launcher
├── gui/main_window.py          # Enhanced GUI (updated)
└── ENHANCED_TTM_SQUEEZE_README.md
```

## Configuration

### TTM Squeeze Parameters
```python
detector = TTMSqueezeDetector(
    bb_length=20,           # Bollinger Bands period
    bb_multiplier=2.0,      # BB standard deviation multiplier
    kc_length=20,           # Keltner Channel period
    kc_multiplier=1.5,      # KC ATR multiplier
    min_squeeze_bars=6,     # Minimum squeeze duration
    momentum_length=20      # Momentum calculation period
)
```

### Trailing Stop Settings
```python
# In config.json
{
    "trailing_stop": {
        "atr_multiplier": 2.0,
        "percentage": 0.02,
        "update_frequency": 60
    }
}
```

## Signal Interpretation

### Enhanced Early Signal Priority
1. **🚀 ENHANCED** - Highest priority, yellow phase transition
2. **⚡ EARLY** - Regular early signal, backup detection
3. **🟡 YELLOW** - Rising negative momentum, watch closely
4. **🔴 RED** - Decreasing negative momentum, building energy
5. **⏳ SQUEEZE** - Active squeeze, waiting for momentum shift

### Trading Recommendations

**For Enhanced Signals (🚀)**:
- Enter immediately with tight stops
- Use smaller position sizes due to early entry
- Set trailing stops to capture momentum

**For Yellow Phase (🟡)**:
- Prepare for potential breakout
- Monitor for enhanced signal trigger
- Consider scaling into position

**For Red Phase (🔴)**:
- Watch for transition to yellow
- Prepare entry orders
- Avoid entering during red phase

## Performance Optimization

### Real-time Scanning
- Scans run every 5 minutes during market hours
- Filters for active signals only
- Minimal API calls to preserve rate limits

### Memory Management
- Rolling calculations for efficiency
- Automatic cleanup of old data
- Optimized for continuous operation

### Error Handling
- Robust error recovery
- Graceful degradation on API failures
- Comprehensive logging for debugging

## Future Enhancements

Potential improvements for advanced implementations:
- Multi-timeframe analysis
- Adaptive parameter optimization
- Machine learning signal filtering
- Options flow confirmation
- Sector rotation analysis
- Automated position sizing based on volatility
- Integration with additional data sources
- Advanced risk management algorithms
