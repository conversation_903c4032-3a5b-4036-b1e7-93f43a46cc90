# Enhanced TTM Squeeze Detection Algorithm

## Overview

This enhanced TTM Squeeze implementation provides **early breakout signal detection** by identifying momentum transitions during the critical "yellow phase" - when momentum is still negative but beginning to rise. This provides traders with earlier entry opportunities compared to traditional implementations that wait for positive momentum.

## Key Enhancements

### 1. **Red Histogram Phase Detection**
- Identifies 4-5 consecutive bars where momentum is negative AND decreasing
- Each bar must be lower than the previous (downward momentum trend)
- Represents the compression phase building up energy for potential breakout

### 2. **Yellow Transition Phase Detection**  
- Detects 2+ consecutive bars where momentum is still negative BUT rising
- Momentum values are higher than previous red bars but still below zero
- This is the critical early warning phase before momentum turns positive

### 3. **Enhanced Early Signal Generation**
- Triggers during the yellow phase (proactive vs reactive)
- Combines multiple confirmation factors:
  - Active squeeze for 6+ bars
  - Current yellow phase detection
  - Momentum acceleration analysis
  - Volume confirmation (when available)
  - Recent red phase history

## Algorithm Components

### Core TTM Squeeze Elements
```python
# Bollinger Bands (20-period, 2.0 std dev)
upper_BB = SMA + (2.0 * StdDev)
lower_BB = SMA - (2.0 * StdDev)

# Keltner Channels (20-period, 1.5 ATR multiplier)
upper_KC = SMA + (1.5 * ATR)
lower_KC = SMA - (1.5 * ATR)

# Squeeze Condition
squeeze_on = (lower_BB > lower_KC) AND (upper_BB < upper_KC)

# Momentum (Linear Regression Slope of 20-period close prices)
momentum = linreg_slope(close, 20)
```

### Enhanced Phase Detection

#### Red Phase Criteria:
- `momentum < 0` (negative)
- `momentum_change < 0` (decreasing)
- `consecutive_count >= 4` (sustained trend)

#### Yellow Phase Criteria:
- `momentum < 0` (still negative)
- `momentum_change > 0` (rising)
- `consecutive_count >= 2` (confirmed trend)

#### Enhanced Signal Conditions:
1. **Base Requirements:**
   - Squeeze active for 6+ bars
   - Currently in yellow phase
   - Momentum still negative but rising for 2+ bars

2. **Additional Confirmations:**
   - Momentum strength above 10-period average
   - Positive momentum acceleration
   - Volume 20% above 20-period average (if available)
   - Recent red phase within last 8 bars

## Signal Types

### 1. **Enhanced Early Signal** (Primary)
- Triggers during yellow phase transition
- Highest priority signal with multiple confirmations
- Provides earliest entry opportunity

### 2. **Regular Early Signal** (Secondary)
- Original early detection logic
- Backup signal for edge cases

### 3. **Combined TTM Signal**
- Union of enhanced and regular signals
- Comprehensive signal coverage

## Usage Example

```python
from ttm_squeeze import TTMSqueezeDetector

# Initialize detector
detector = TTMSqueezeDetector(
    bb_length=20,           # Bollinger Bands period
    bb_multiplier=2.0,      # BB standard deviation multiplier
    kc_length=20,           # Keltner Channel period  
    kc_multiplier=1.5,      # KC ATR multiplier
    min_squeeze_bars=6,     # Minimum squeeze duration
    momentum_length=20      # Momentum calculation period
)

# Calculate indicators and signals
df_with_signals = detector.calculate_indicators(df)

# Get current signal status
signal_info = detector.get_current_signal(df_with_signals)

print(f"Enhanced Early Signal: {signal_info['enhanced_early_signal']}")
print(f"Yellow Phase: {signal_info['yellow_phase']}")
print(f"Red Phase: {signal_info['red_phase']}")
print(f"Momentum: {signal_info['momentum']:.4f}")
```

## Signal Information Output

```python
{
    'has_signal': bool,              # Combined signal status
    'early_signal': bool,            # Regular early signal
    'enhanced_early_signal': bool,   # Enhanced early signal
    'squeeze_active': bool,          # Current squeeze status
    'squeeze_count': int,            # Consecutive squeeze bars
    'momentum': float,               # Current momentum value
    'momentum_strength': float,      # Absolute momentum value
    'red_phase': bool,              # In red histogram phase
    'yellow_phase': bool,           # In yellow transition phase
    'current_price': float,         # Latest close price
    'squeeze_strength': float,      # Squeeze tightness measure
    'timestamp': datetime           # Signal timestamp
}
```

## Trading Strategy Integration

### Entry Signals
- **Primary**: Enhanced early signal during yellow phase
- **Confirmation**: Volume above average, momentum acceleration
- **Direction**: Determined by momentum sign when it turns positive

### Risk Management
- **Stop Loss**: Below/above recent squeeze low/high
- **Position Size**: Based on ATR and squeeze strength
- **Exit**: When momentum shows signs of exhaustion

### Advantages Over Traditional TTM Squeeze
1. **Earlier Entry**: Signals before momentum turns positive
2. **Better Risk/Reward**: Enter closer to breakout initiation
3. **Reduced Slippage**: Avoid late entries after momentum confirmation
4. **Higher Probability**: Multiple confirmation factors reduce false signals

## Testing and Validation

Run the test script to see the enhanced algorithm in action:

```bash
python test_enhanced_ttm_squeeze.py
```

This will:
- Test with sample data showing squeeze patterns
- Analyze real market data for multiple symbols
- Generate comprehensive visualization plots
- Display signal analysis and timing comparisons

## Visualization Features

The test script creates three-panel charts showing:

1. **Price Chart**: Bollinger Bands, Keltner Channels, squeeze periods, and signal markers
2. **Momentum Histogram**: Color-coded phases (red, yellow, green) with signal timing
3. **Squeeze Analysis**: Duration tracking and momentum strength indicators

## Performance Considerations

- Requires minimum 20 bars for momentum calculation
- Enhanced analysis needs 15+ bars for reliable signals
- Real-time performance optimized for streaming data
- Memory efficient with rolling calculations

## Future Enhancements

Potential improvements for advanced implementations:
- Multi-timeframe analysis
- Adaptive parameter optimization
- Machine learning signal filtering
- Options flow confirmation
- Sector rotation analysis
