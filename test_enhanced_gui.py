"""
Test Enhanced GUI Components
Simple test to verify the enhanced signals tab is working
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_signals_tab():
    """Test the enhanced signals tab components"""
    print("Testing Enhanced Signals Tab Components...")
    
    # Create test window
    root = tk.Tk()
    root.title("Enhanced Signals Tab Test")
    root.geometry("800x600")
    
    # Create notebook
    notebook = ttk.Notebook(root)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Create enhanced signals frame
    signals_frame = ttk.Frame(notebook)
    notebook.add(signals_frame, text="Enhanced Signals")
    
    # Control panel
    control_frame = ttk.LabelFrame(signals_frame, text="Enhanced TTM Squeeze Controls")
    control_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # Buttons row
    buttons_row = ttk.Frame(control_frame)
    buttons_row.pack(fill=tk.X, padx=5, pady=5)
    
    # Test scan button
    def test_scan():
        print("Test scan button clicked!")
        progress_bar.config(value=50)
        status_label.config(text="Testing scan progress...")
        signals_label.config(text="Found 2 test signals")
    
    scan_button = ttk.Button(buttons_row, text="Test Scan", command=test_scan)
    scan_button.pack(side=tk.LEFT, padx=5)
    
    cancel_button = ttk.Button(buttons_row, text="Cancel", state=tk.DISABLED)
    cancel_button.pack(side=tk.LEFT, padx=5)
    
    # Auto-scan toggle
    auto_scan_var = tk.BooleanVar()
    ttk.Checkbutton(buttons_row, text="Auto-scan (5 min)", 
                   variable=auto_scan_var).pack(side=tk.LEFT, padx=15)
    
    # Clear cache button
    def clear_cache():
        print("Cache cleared!")
        
    ttk.Button(buttons_row, text="Clear Cache", command=clear_cache).pack(side=tk.RIGHT, padx=5)
    
    # Progress and status row
    progress_row = ttk.Frame(control_frame)
    progress_row.pack(fill=tk.X, padx=5, pady=5)
    
    # Progress bar
    progress_bar = ttk.Progressbar(progress_row, mode='determinate', maximum=100)
    progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
    
    # Status labels
    status_col = ttk.Frame(progress_row)
    status_col.pack(side=tk.RIGHT)
    
    status_label = ttk.Label(status_col, text="Ready to scan")
    status_label.pack(anchor=tk.E)
    
    signals_label = ttk.Label(status_col, text="")
    signals_label.pack(anchor=tk.E)
    
    last_scan_label = ttk.Label(status_col, text="", font=('TkDefaultFont', 8))
    last_scan_label.pack(anchor=tk.E)
    
    # Signal display
    signals_display_frame = ttk.LabelFrame(signals_frame, text="Enhanced Signals")
    signals_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # Enhanced signals tree
    columns = ('Symbol', 'Price', 'Signal Type', 'Phase', 'Momentum', 'Squeeze Count', 'Strength')
    signals_tree = ttk.Treeview(signals_display_frame, columns=columns, show='headings')
    
    for col in columns:
        signals_tree.heading(col, text=col)
        signals_tree.column(col, width=100)
    
    # Add test data
    test_signals = [
        ('AAPL', '$150.25', '🚀 ENHANCED', '🟡 YELLOW', '-0.0234', '8', '0.156'),
        ('TSLA', '$200.50', '🟡 YELLOW', '🟡 YELLOW', '-0.0156', '6', '0.234'),
        ('MSFT', '$300.75', '🔴 RED', '🔴 RED', '-0.0345', '4', '0.123'),
    ]
    
    for signal in test_signals:
        signals_tree.insert('', tk.END, values=signal)
    
    # Configure colors
    signals_tree.tag_configure('enhanced', background='#e8f5e8')
    signals_tree.tag_configure('yellow', background='#fff3cd')
    signals_tree.tag_configure('red', background='#f8d7da')
    
    # Scrollbar
    scrollbar = ttk.Scrollbar(signals_display_frame, orient=tk.VERTICAL, command=signals_tree.yview)
    signals_tree.configure(yscrollcommand=scrollbar.set)
    
    signals_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Manual trading panel
    trading_frame = ttk.LabelFrame(signals_frame, text="Manual Trading")
    trading_frame.pack(fill=tk.X, padx=5, pady=5)
    
    def test_manual_trade():
        print("Manual trade dialog would open here")
    
    def test_quick_trade():
        print("Quick trade executed")
    
    ttk.Button(trading_frame, text="Manual Trade Selected", 
              command=test_manual_trade).pack(side=tk.LEFT, padx=5, pady=5)
    
    ttk.Button(trading_frame, text="Quick Trade (Default Params)", 
              command=test_quick_trade).pack(side=tk.LEFT, padx=5, pady=5)
    
    print("✅ Enhanced Signals Tab created successfully!")
    print("✅ All components loaded:")
    print("   - Progress bar")
    print("   - Status labels")
    print("   - Scan/Cancel buttons")
    print("   - Auto-scan checkbox")
    print("   - Clear cache button")
    print("   - Signals table with test data")
    print("   - Manual trading buttons")
    
    # Run the test GUI
    root.mainloop()

if __name__ == "__main__":
    print("🧪 ENHANCED SIGNALS TAB TEST")
    print("=" * 40)
    test_enhanced_signals_tab()
