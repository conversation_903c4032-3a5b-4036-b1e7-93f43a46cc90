"""
Main GUI Window for TTM Squeeze Trading Application
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from datetime import datetime
import logging
from typing import Dict, Any

from config import config
from scanner import MarketScanner
from trade_manager import TradeManager
from alpaca_client import AlpacaClient
from ttm_squeeze import TTMSqueezeDetector
from trailing_stop_manager import TrailingStopManager

class MainWindow:
    """Main application window"""
    
    def __init__(self):
        """Initialize main window"""
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.scanner = MarketScanner(callback=self.on_new_signals)
        self.trade_manager = TradeManager()

        # Try to initialize Alpaca client, fall back to demo mode if it fails
        self.alpaca_client = AlpacaClient()
        if not self.alpaca_client.is_connected():
            self.logger.warning("Alpaca connection failed, enabling demo mode")
            from demo_mode import DemoAlpacaClient
            self.alpaca_client = DemoAlpacaClient()
            self.demo_mode = True
        else:
            self.demo_mode = False

        self.ttm_detector = TTMSqueezeDetector()
        self.trailing_stop_manager = TrailingStopManager(self.alpaca_client)
        
        # GUI state
        self.current_signals = {}
        self.selected_symbol = None
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("TTM Squeeze Trading Application")
        self.root.geometry("1400x900")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Setup GUI
        self.setup_gui()
        self.setup_status_bar()
        
        # Start periodic updates
        self.update_gui()
        
        self.logger.info("Main window initialized")
    
    def setup_gui(self):
        """Setup the main GUI layout"""
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.create_scanner_tab()
        self.create_enhanced_signals_tab()
        self.create_positions_tab()
        self.create_trailing_stops_tab()
        self.create_settings_tab()
        self.create_logs_tab()
    
    def create_scanner_tab(self):
        """Create the market scanner tab"""
        scanner_frame = ttk.Frame(self.notebook)
        self.notebook.add(scanner_frame, text="Market Scanner")
        
        # Control panel
        control_frame = ttk.LabelFrame(scanner_frame, text="Scanner Controls")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Scanner buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_button = ttk.Button(button_frame, text="Start Scanner", command=self.start_scanner)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop Scanner", command=self.stop_scanner, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.manual_scan_button = ttk.Button(button_frame, text="Manual Scan", command=self.manual_scan)
        self.manual_scan_button.pack(side=tk.LEFT, padx=5)
        
        # Status labels
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.scanner_status_label = ttk.Label(status_frame, text="Scanner: Stopped")
        self.scanner_status_label.pack(side=tk.LEFT, padx=5)
        
        self.last_scan_label = ttk.Label(status_frame, text="Last Scan: Never")
        self.last_scan_label.pack(side=tk.LEFT, padx=20)
        
        self.signals_count_label = ttk.Label(status_frame, text="Signals: 0")
        self.signals_count_label.pack(side=tk.LEFT, padx=20)
        
        # Opportunities table
        opportunities_frame = ttk.LabelFrame(scanner_frame, text="Trading Opportunities")
        opportunities_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview for opportunities
        columns = ('Symbol', 'Price', 'Squeeze Count', 'Momentum', 'Strength', 'Signal', 'Last Update')
        self.opportunities_tree = ttk.Treeview(opportunities_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.opportunities_tree.heading(col, text=col)
            if col == 'Symbol':
                self.opportunities_tree.column(col, width=80)
            elif col in ['Price', 'Momentum', 'Strength']:
                self.opportunities_tree.column(col, width=100)
            elif col == 'Squeeze Count':
                self.opportunities_tree.column(col, width=120)
            elif col == 'Signal':
                self.opportunities_tree.column(col, width=80)
            else:
                self.opportunities_tree.column(col, width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(opportunities_frame, orient=tk.VERTICAL, command=self.opportunities_tree.yview)
        self.opportunities_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.opportunities_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind selection event
        self.opportunities_tree.bind('<<TreeviewSelect>>', self.on_opportunity_select)
        
        # Trade action frame
        action_frame = ttk.LabelFrame(scanner_frame, text="TRADING ACTIONS")
        action_frame.pack(fill=tk.X, padx=5, pady=5)

        # Main trading buttons row
        main_buttons = ttk.Frame(action_frame)
        main_buttons.pack(fill=tk.X, padx=5, pady=5)

        self.trade_button = ttk.Button(main_buttons, text="PLACE TRADE", command=self.place_trade, state=tk.DISABLED)
        self.trade_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.quick_buy_button = ttk.Button(main_buttons, text="QUICK BUY", command=self.quick_buy, state=tk.DISABLED)
        self.quick_buy_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.close_all_button = ttk.Button(main_buttons, text="CLOSE ALL", command=self.close_all_positions)
        self.close_all_button.pack(side=tk.RIGHT, padx=5, pady=5)

        # Selection and info row
        info_row = ttk.Frame(action_frame)
        info_row.pack(fill=tk.X, padx=5, pady=2)

        self.selected_symbol_label = ttk.Label(info_row, text="No symbol selected", font=('Arial', 10, 'bold'))
        self.selected_symbol_label.pack(side=tk.LEFT, padx=5)

        # Position size controls
        ttk.Label(info_row, text="Position Size:").pack(side=tk.LEFT, padx=(20, 5))
        self.position_size_entry = ttk.Entry(info_row, width=8)
        self.position_size_entry.pack(side=tk.LEFT, padx=2)
        self.position_size_entry.insert(0, "100")  # Default shares

        ttk.Label(info_row, text="shares").pack(side=tk.LEFT, padx=2)
    
    def create_positions_tab(self):
        """Create the positions management tab"""
        positions_frame = ttk.Frame(self.notebook)
        self.notebook.add(positions_frame, text="Positions")
        
        # Account info frame
        account_frame = ttk.LabelFrame(positions_frame, text="Account Information")
        account_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.account_info_text = tk.Text(account_frame, height=4, state=tk.DISABLED)
        self.account_info_text.pack(fill=tk.X, padx=5, pady=5)
        
        # Active trades frame
        active_trades_frame = ttk.LabelFrame(positions_frame, text="Active Trades")
        active_trades_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Active trades treeview
        trade_columns = ('Symbol', 'Quantity', 'Entry Price', 'Current Price', 'P&L', 'P&L %', 'Stop Loss', 'Take Profit', 'Status')
        self.trades_tree = ttk.Treeview(active_trades_frame, columns=trade_columns, show='headings', height=10)
        
        for col in trade_columns:
            self.trades_tree.heading(col, text=col)
            self.trades_tree.column(col, width=100)
        
        trades_scrollbar = ttk.Scrollbar(active_trades_frame, orient=tk.VERTICAL, command=self.trades_tree.yview)
        self.trades_tree.configure(yscrollcommand=trades_scrollbar.set)
        
        self.trades_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        trades_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Performance summary frame
        performance_frame = ttk.LabelFrame(positions_frame, text="Performance Summary")
        performance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.performance_text = tk.Text(performance_frame, height=6, state=tk.DISABLED)
        self.performance_text.pack(fill=tk.X, padx=5, pady=5)

    def create_enhanced_signals_tab(self):
        """Create enhanced TTM Squeeze signals tab"""
        signals_frame = ttk.Frame(self.notebook)
        self.notebook.add(signals_frame, text="Enhanced Signals")

        # Control panel
        control_frame = ttk.LabelFrame(signals_frame, text="Enhanced TTM Squeeze Controls")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # Scan button
        ttk.Button(control_frame, text="Scan for Enhanced Signals",
                  command=self.scan_enhanced_signals).pack(side=tk.LEFT, padx=5, pady=5)

        # Auto-scan toggle
        self.auto_scan_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="Auto-scan (5 min)",
                       variable=self.auto_scan_var).pack(side=tk.LEFT, padx=5)

        # Signal display
        signals_display_frame = ttk.LabelFrame(signals_frame, text="Enhanced Signals")
        signals_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Enhanced signals tree
        columns = ('Symbol', 'Price', 'Signal Type', 'Phase', 'Momentum', 'Squeeze Count', 'Strength')
        self.enhanced_signals_tree = ttk.Treeview(signals_display_frame, columns=columns, show='headings')

        for col in columns:
            self.enhanced_signals_tree.heading(col, text=col)
            self.enhanced_signals_tree.column(col, width=100)

        # Scrollbar for signals tree
        signals_scrollbar = ttk.Scrollbar(signals_display_frame, orient=tk.VERTICAL,
                                        command=self.enhanced_signals_tree.yview)
        self.enhanced_signals_tree.configure(yscrollcommand=signals_scrollbar.set)

        self.enhanced_signals_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        signals_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind double-click for manual trading
        self.enhanced_signals_tree.bind('<Double-1>', self.on_enhanced_signal_double_click)

        # Manual trading panel
        trading_frame = ttk.LabelFrame(signals_frame, text="Manual Trading")
        trading_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(trading_frame, text="Manual Trade Selected",
                  command=self.open_manual_trading_dialog).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(trading_frame, text="Quick Trade (Default Params)",
                  command=self.quick_trade_selected).pack(side=tk.LEFT, padx=5, pady=5)

    def create_trailing_stops_tab(self):
        """Create trailing stops management tab"""
        trailing_frame = ttk.Frame(self.notebook)
        self.notebook.add(trailing_frame, text="Trailing Stops")

        # Control panel
        control_frame = ttk.LabelFrame(trailing_frame, text="Trailing Stop Controls")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="Refresh Trailing Stops",
                  command=self.refresh_trailing_stops).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(control_frame, text="Add Trailing Stop",
                  command=self.add_trailing_stop_dialog).pack(side=tk.LEFT, padx=5, pady=5)

        # Trailing stops display
        trailing_display_frame = ttk.LabelFrame(trailing_frame, text="Active Trailing Stops")
        trailing_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Trailing stops tree
        trailing_columns = ('Symbol', 'Entry', 'Current', 'Stop', 'Highest', 'Type', 'Updated')
        self.trailing_stops_tree = ttk.Treeview(trailing_display_frame, columns=trailing_columns, show='headings')

        for col in trailing_columns:
            self.trailing_stops_tree.heading(col, text=col)
            self.trailing_stops_tree.column(col, width=100)

        # Scrollbar for trailing stops tree
        trailing_scrollbar = ttk.Scrollbar(trailing_display_frame, orient=tk.VERTICAL,
                                         command=self.trailing_stops_tree.yview)
        self.trailing_stops_tree.configure(yscrollcommand=trailing_scrollbar.set)

        self.trailing_stops_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        trailing_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Settings panel
        settings_frame = ttk.LabelFrame(trailing_frame, text="Trailing Stop Settings")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)

        # ATR Multiplier
        ttk.Label(settings_frame, text="ATR Multiplier:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.atr_multiplier_var = tk.DoubleVar(value=2.0)
        ttk.Entry(settings_frame, textvariable=self.atr_multiplier_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        # Percentage Trail
        ttk.Label(settings_frame, text="Percentage Trail (%):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.percentage_trail_var = tk.DoubleVar(value=2.0)
        ttk.Entry(settings_frame, textvariable=self.percentage_trail_var, width=10).grid(row=0, column=3, padx=5, pady=2)

    def create_settings_tab(self):
        """Create the settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Settings")
        
        # Trading settings
        trading_frame = ttk.LabelFrame(settings_frame, text="Trading Settings")
        trading_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Position size
        ttk.Label(trading_frame, text="Position Size (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.position_size_var = tk.DoubleVar(value=config.get("trading.position_size_percent", 2.0))
        ttk.Entry(trading_frame, textvariable=self.position_size_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        # Stop loss
        ttk.Label(trading_frame, text="Stop Loss (%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.stop_loss_var = tk.DoubleVar(value=config.get("trading.stop_loss_percent", 2.0))
        ttk.Entry(trading_frame, textvariable=self.stop_loss_var, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        # Take profit ratio
        ttk.Label(trading_frame, text="Take Profit Ratio:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.take_profit_var = tk.DoubleVar(value=config.get("trading.take_profit_ratio", 2.0))
        ttk.Entry(trading_frame, textvariable=self.take_profit_var, width=10).grid(row=2, column=1, padx=5, pady=2)
        
        # Max concurrent trades
        ttk.Label(trading_frame, text="Max Concurrent Trades:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_trades_var = tk.IntVar(value=config.get("trading.max_concurrent_trades", 5))
        ttk.Entry(trading_frame, textvariable=self.max_trades_var, width=10).grid(row=3, column=1, padx=5, pady=2)
        
        # Save settings button
        ttk.Button(trading_frame, text="Save Settings", command=self.save_settings).grid(row=4, column=0, columnspan=2, pady=10)
        
        # Watchlist frame
        watchlist_frame = ttk.LabelFrame(settings_frame, text="Watchlist Management")
        watchlist_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Watchlist text area
        self.watchlist_text = scrolledtext.ScrolledText(watchlist_frame, height=15)
        self.watchlist_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Load current watchlist
        self.load_watchlist()
        
        # Watchlist buttons
        watchlist_buttons = ttk.Frame(watchlist_frame)
        watchlist_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(watchlist_buttons, text="Save Watchlist", command=self.save_watchlist).pack(side=tk.LEFT, padx=5)
        ttk.Button(watchlist_buttons, text="Reload Watchlist", command=self.load_watchlist).pack(side=tk.LEFT, padx=5)
    
    def create_logs_tab(self):
        """Create the logs tab"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="Logs")
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(logs_frame, height=30)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Clear logs button
        ttk.Button(logs_frame, text="Clear Logs", command=self.clear_logs).pack(pady=5)
    
    def setup_status_bar(self):
        """Setup status bar at bottom of window"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.connection_status_label = ttk.Label(self.status_bar, text="Checking connection...")
        self.connection_status_label.pack(side=tk.LEFT, padx=5)
        
        self.market_status_label = ttk.Label(self.status_bar, text="Market: Unknown")
        self.market_status_label.pack(side=tk.LEFT, padx=20)
    
    def start_scanner(self):
        """Start the market scanner"""
        try:
            self.scanner.start_scanning()
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.scanner_status_label.config(text="Scanner: Running")
            self.log_message("Market scanner started")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start scanner: {e}")
    
    def stop_scanner(self):
        """Stop the market scanner"""
        try:
            self.scanner.stop_scanning()
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.scanner_status_label.config(text="Scanner: Stopped")
            self.log_message("Market scanner stopped")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop scanner: {e}")
    
    def manual_scan(self):
        """Perform manual scan"""
        def scan_thread():
            try:
                self.log_message("Starting manual scan...")
                signals = self.scanner.manual_scan()
                self.current_signals = signals
                self.root.after(0, self.update_opportunities_table)
                self.root.after(0, lambda: self.log_message(f"Manual scan completed - found {len(signals)} signals"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Manual scan failed: {e}"))
        
        threading.Thread(target=scan_thread, daemon=True).start()
    
    def on_new_signals(self, signals: Dict[str, Any]):
        """Callback for new signals from scanner"""
        self.current_signals = signals
        self.root.after(0, self.update_opportunities_table)
    
    def update_opportunities_table(self):
        """Update the opportunities table with current signals"""
        # Clear existing items
        for item in self.opportunities_tree.get_children():
            self.opportunities_tree.delete(item)
        
        # Add current signals
        for symbol, signal in self.current_signals.items():
            values = (
                symbol,
                f"${signal.get('current_price', 0):.2f}",
                signal.get('squeeze_count', 0),
                f"{signal.get('momentum', 0):.4f}",
                f"{signal.get('squeeze_strength', 0):.3f}",
                "YES" if signal.get('has_signal', False) else "SQUEEZE",
                datetime.now().strftime("%H:%M:%S")
            )
            
            # Color code based on signal strength
            item = self.opportunities_tree.insert('', tk.END, values=values)
            if signal.get('has_signal', False):
                self.opportunities_tree.set(item, 'Signal', 'SIGNAL')
        
        # Update signals count
        self.signals_count_label.config(text=f"Signals: {len(self.current_signals)}")
    
    def on_opportunity_select(self, event):
        """Handle opportunity selection"""
        selection = self.opportunities_tree.selection()
        if selection:
            item = selection[0]
            symbol = self.opportunities_tree.item(item, 'values')[0]
            self.selected_symbol = symbol
            self.selected_symbol_label.config(text=f"Selected: {symbol}")
            self.trade_button.config(state=tk.NORMAL)
            self.quick_buy_button.config(state=tk.NORMAL)
        else:
            self.selected_symbol = None
            self.selected_symbol_label.config(text="No symbol selected")
            self.trade_button.config(state=tk.DISABLED)
            self.quick_buy_button.config(state=tk.DISABLED)
    
    def place_trade(self):
        """Place trade for selected symbol"""
        if not self.selected_symbol or self.selected_symbol not in self.current_signals:
            messagebox.showwarning("Warning", "Please select a valid symbol")
            return
        
        signal_info = self.current_signals[self.selected_symbol]
        
        # Confirm trade
        msg = (f"Place trade for {self.selected_symbol}?\n\n"
               f"Price: ${signal_info.get('current_price', 0):.2f}\n"
               f"Squeeze Count: {signal_info.get('squeeze_count', 0)} bars\n"
               f"Momentum: {signal_info.get('momentum', 0):.4f}")
        
        if messagebox.askyesno("Confirm Trade", msg):
            def trade_thread():
                try:
                    order_id = self.trade_manager.place_trade(signal_info)
                    if order_id:
                        self.root.after(0, lambda: self.log_message(f"Trade placed for {self.selected_symbol} - Order ID: {order_id}"))
                        self.root.after(0, self.update_positions_tab)
                    else:
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to place trade for {self.selected_symbol}"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Trade error: {e}"))
            
            threading.Thread(target=trade_thread, daemon=True).start()

    def quick_buy(self):
        """Quick buy for selected symbol without TTM Squeeze confirmation"""
        if not self.selected_symbol:
            messagebox.showwarning("Warning", "Please select a symbol first")
            return

        try:
            # Get position size from entry
            shares = int(self.position_size_entry.get())
            if shares <= 0:
                messagebox.showerror("Error", "Invalid position size")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number of shares")
            return

        # Get current price
        current_price = self.alpaca_client.get_current_price(self.selected_symbol)
        if not current_price:
            messagebox.showerror("Error", f"Could not get current price for {self.selected_symbol}")
            return

        # Calculate stop loss and take profit
        stop_loss_percent = self.stop_loss_var.get() if hasattr(self, 'stop_loss_var') else 2.0
        take_profit_ratio = self.take_profit_var.get() if hasattr(self, 'take_profit_var') else 2.0

        stop_loss_price = current_price * (1 - stop_loss_percent / 100)
        stop_loss_distance = current_price - stop_loss_price
        take_profit_price = current_price + (stop_loss_distance * take_profit_ratio)

        # Confirm trade
        msg = (f"Quick Buy {self.selected_symbol}?\n\n"
               f"Shares: {shares}\n"
               f"Price: ${current_price:.2f}\n"
               f"Stop Loss: ${stop_loss_price:.2f}\n"
               f"Take Profit: ${take_profit_price:.2f}\n"
               f"Total Cost: ${current_price * shares:.2f}")

        if messagebox.askyesno("Confirm Quick Buy", msg):
            def trade_thread():
                try:
                    order_id = self.alpaca_client.place_bracket_order(
                        symbol=self.selected_symbol,
                        qty=shares,
                        side='buy',
                        stop_loss_price=stop_loss_price,
                        take_profit_price=take_profit_price
                    )

                    if order_id:
                        # Track the trade manually
                        trade_info = {
                            'symbol': self.selected_symbol,
                            'order_id': order_id,
                            'quantity': shares,
                            'entry_price': current_price,
                            'stop_loss': stop_loss_price,
                            'take_profit': take_profit_price,
                            'entry_time': datetime.now(),
                            'squeeze_count': 0,  # Not a TTM squeeze trade
                            'momentum': 0,
                            'status': 'pending'
                        }

                        self.trade_manager.active_trades[self.selected_symbol] = trade_info
                        self.trade_manager._log_trade_action(trade_info, 'ENTRY')

                        self.root.after(0, lambda: self.log_message(f"Quick buy order placed for {self.selected_symbol} - Order ID: {order_id}"))
                        self.root.after(0, self.update_positions_tab)
                    else:
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to place quick buy order for {self.selected_symbol}"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Quick buy error: {e}"))

            threading.Thread(target=trade_thread, daemon=True).start()

    def close_all_positions(self):
        """Close all open positions"""
        active_trades = self.trade_manager.get_active_trades()

        if not active_trades:
            messagebox.showinfo("Info", "No active positions to close")
            return

        msg = f"Close all {len(active_trades)} active positions?"
        if messagebox.askyesno("Confirm Close All", msg):
            def close_thread():
                closed_count = 0
                for symbol in list(active_trades.keys()):
                    try:
                        if self.trade_manager.cancel_trade(symbol):
                            closed_count += 1
                    except Exception as e:
                        self.logger.error(f"Error closing position for {symbol}: {e}")

                self.root.after(0, lambda: self.log_message(f"Closed {closed_count} positions"))
                self.root.after(0, self.update_positions_tab)

            threading.Thread(target=close_thread, daemon=True).start()

    def update_positions_tab(self):
        """Update positions tab with current data"""
        # Update account info
        account_info = self.alpaca_client.get_account_info()
        if account_info:
            account_text = (
                f"Buying Power: ${account_info['buying_power']:,.2f}\n"
                f"Cash: ${account_info['cash']:,.2f}\n"
                f"Portfolio Value: ${account_info['portfolio_value']:,.2f}\n"
                f"Equity: ${account_info['equity']:,.2f}"
            )
            self.account_info_text.config(state=tk.NORMAL)
            self.account_info_text.delete(1.0, tk.END)
            self.account_info_text.insert(1.0, account_text)
            self.account_info_text.config(state=tk.DISABLED)
        
        # Update active trades
        self.trade_manager.update_active_trades()
        active_trades = self.trade_manager.get_active_trades()
        
        # Clear trades tree
        for item in self.trades_tree.get_children():
            self.trades_tree.delete(item)
        
        # Add active trades
        for symbol, trade in active_trades.items():
            current_price = self.alpaca_client.get_current_price(symbol) or trade['entry_price']
            pnl = (current_price - trade['entry_price']) * trade['quantity']
            pnl_percent = ((current_price - trade['entry_price']) / trade['entry_price']) * 100
            
            values = (
                symbol,
                trade['quantity'],
                f"${trade['entry_price']:.2f}",
                f"${current_price:.2f}",
                f"${pnl:.2f}",
                f"{pnl_percent:.2f}%",
                f"${trade['stop_loss']:.2f}",
                f"${trade['take_profit']:.2f}",
                trade['status']
            )
            self.trades_tree.insert('', tk.END, values=values)
        
        # Update performance summary
        performance = self.trade_manager.get_performance_summary()
        if performance:
            perf_text = (
                f"Total Trades: {performance['total_trades']}\n"
                f"Winning Trades: {performance['winning_trades']}\n"
                f"Losing Trades: {performance['losing_trades']}\n"
                f"Win Rate: {performance['win_rate']:.1f}%\n"
                f"Total P&L: ${performance['total_pnl']:.2f}\n"
                f"Profit Factor: {performance['profit_factor']:.2f}"
            )
            self.performance_text.config(state=tk.NORMAL)
            self.performance_text.delete(1.0, tk.END)
            self.performance_text.insert(1.0, perf_text)
            self.performance_text.config(state=tk.DISABLED)
    
    def save_settings(self):
        """Save trading settings"""
        try:
            config.set("trading.position_size_percent", self.position_size_var.get())
            config.set("trading.stop_loss_percent", self.stop_loss_var.get())
            config.set("trading.take_profit_ratio", self.take_profit_var.get())
            config.set("trading.max_concurrent_trades", self.max_trades_var.get())
            config.save()
            messagebox.showinfo("Success", "Settings saved successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")
    
    def load_watchlist(self):
        """Load watchlist into text area"""
        try:
            watchlist = self.scanner.get_watchlist()
            self.watchlist_text.delete(1.0, tk.END)
            self.watchlist_text.insert(1.0, '\n'.join(watchlist))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load watchlist: {e}")
    
    def save_watchlist(self):
        """Save watchlist from text area"""
        try:
            content = self.watchlist_text.get(1.0, tk.END).strip()
            symbols = [line.strip().upper() for line in content.split('\n') if line.strip()]
            self.scanner.update_watchlist(symbols)
            messagebox.showinfo("Success", f"Watchlist saved with {len(symbols)} symbols")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save watchlist: {e}")
    
    def clear_logs(self):
        """Clear log text area"""
        self.log_text.delete(1.0, tk.END)
    
    def log_message(self, message: str):
        """Add message to log text area"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def update_gui(self):
        """Periodic GUI updates"""
        try:
            # Update connection status
            if self.alpaca_client.is_connected():
                if hasattr(self, 'demo_mode') and self.demo_mode:
                    self.connection_status_label.config(text="Alpaca: Demo Mode")
                else:
                    self.connection_status_label.config(text="Alpaca: Connected")
            else:
                self.connection_status_label.config(text="Alpaca: Disconnected")
            
            # Update market status
            if self.alpaca_client.is_market_open():
                self.market_status_label.config(text="Market: Open")
            else:
                self.market_status_label.config(text="Market: Closed")
            
            # Update last scan time
            scanner_status = self.scanner.get_scanner_status()
            if scanner_status['last_scan_time']:
                last_scan = scanner_status['last_scan_time'].strftime("%H:%M:%S")
                self.last_scan_label.config(text=f"Last Scan: {last_scan}")
            
            # Update positions if on positions tab
            current_tab = self.notebook.tab(self.notebook.select(), "text")
            if current_tab == "Positions":
                self.update_positions_tab()
            
        except Exception as e:
            self.logger.error(f"Error updating GUI: {e}")
        
        # Schedule next update
        self.root.after(30000, self.update_gui)  # Update every 30 seconds
    
    def on_closing(self):
        """Handle window closing"""
        try:
            self.scanner.stop_scanning()
            self.root.destroy()
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
            self.root.destroy()
    
    def scan_enhanced_signals(self):
        """Scan for enhanced TTM Squeeze signals"""
        def scan_thread():
            try:
                self.log_message("Scanning for enhanced TTM Squeeze signals...")

                # Get watchlist
                watchlist = self.scanner.get_watchlist()

                # Get data for all symbols
                data_dict = self.alpaca_client.get_multiple_historical_data(
                    watchlist, timeframe='1Day', limit=100
                )

                enhanced_signals = {}

                for symbol, df in data_dict.items():
                    try:
                        # Calculate TTM Squeeze indicators
                        df_with_signals = self.ttm_detector.calculate_indicators(df)
                        signal_info = self.ttm_detector.get_current_signal(df_with_signals)

                        # Filter for enhanced signals
                        if (signal_info['enhanced_early_signal'] or
                            signal_info['yellow_phase'] or
                            signal_info['red_phase']):
                            enhanced_signals[symbol] = signal_info

                    except Exception as e:
                        self.logger.error(f"Error processing {symbol}: {e}")
                        continue

                # Update GUI
                self.root.after(0, lambda: self.update_enhanced_signals_table(enhanced_signals))
                self.root.after(0, lambda: self.log_message(f"Enhanced scan completed - found {len(enhanced_signals)} signals"))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Enhanced scan failed: {e}"))

        threading.Thread(target=scan_thread, daemon=True).start()

    def update_enhanced_signals_table(self, signals):
        """Update enhanced signals table"""
        # Clear existing items
        for item in self.enhanced_signals_tree.get_children():
            self.enhanced_signals_tree.delete(item)

        # Add enhanced signals
        for symbol, signal in signals.items():
            # Determine signal type
            if signal['enhanced_early_signal']:
                signal_type = "🚀 ENHANCED"
            elif signal['early_signal']:
                signal_type = "⚡ EARLY"
            elif signal['yellow_phase']:
                signal_type = "🟡 YELLOW"
            elif signal['red_phase']:
                signal_type = "🔴 RED"
            else:
                signal_type = "⏳ SQUEEZE"

            # Determine phase
            if signal['yellow_phase']:
                phase = "🟡 YELLOW"
            elif signal['red_phase']:
                phase = "🔴 RED"
            elif signal['momentum'] > 0:
                phase = "🟢 GREEN"
            else:
                phase = "⚪ NEUTRAL"

            values = (
                symbol,
                f"${signal['current_price']:.2f}",
                signal_type,
                phase,
                f"{signal['momentum']:.4f}",
                signal['squeeze_count'],
                f"{signal['squeeze_strength']:.3f}"
            )

            item = self.enhanced_signals_tree.insert('', tk.END, values=values)

            # Color code based on signal type
            if signal['enhanced_early_signal']:
                self.enhanced_signals_tree.set(item, 'Signal Type', '🚀 ENHANCED')

    def on_enhanced_signal_double_click(self, event):
        """Handle double-click on enhanced signal"""
        selection = self.enhanced_signals_tree.selection()
        if selection:
            item = selection[0]
            symbol = self.enhanced_signals_tree.item(item, 'values')[0]
            self.open_manual_trading_dialog(symbol)

    def open_manual_trading_dialog(self, symbol=None):
        """Open manual trading dialog"""
        if not symbol:
            selection = self.enhanced_signals_tree.selection()
            if not selection:
                messagebox.showwarning("Warning", "Please select a signal first")
                return
            item = selection[0]
            symbol = self.enhanced_signals_tree.item(item, 'values')[0]

        # Create trading dialog window
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Manual Trade - {symbol}")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Get current price
        current_price = self.alpaca_client.get_current_price(symbol)
        if not current_price:
            messagebox.showerror("Error", f"Could not get current price for {symbol}")
            dialog.destroy()
            return

        # Trade parameters frame
        params_frame = ttk.LabelFrame(dialog, text="Trade Parameters")
        params_frame.pack(fill=tk.X, padx=10, pady=10)

        # Quantity
        ttk.Label(params_frame, text="Quantity:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        quantity_var = tk.IntVar(value=100)
        ttk.Entry(params_frame, textvariable=quantity_var, width=15).grid(row=0, column=1, padx=5, pady=5)

        # Entry price
        ttk.Label(params_frame, text="Entry Price:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        entry_price_var = tk.DoubleVar(value=current_price)
        ttk.Entry(params_frame, textvariable=entry_price_var, width=15).grid(row=1, column=1, padx=5, pady=5)

        # Stop loss
        ttk.Label(params_frame, text="Stop Loss:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        stop_loss_var = tk.DoubleVar(value=current_price * 0.98)
        ttk.Entry(params_frame, textvariable=stop_loss_var, width=15).grid(row=2, column=1, padx=5, pady=5)

        # Take profit
        ttk.Label(params_frame, text="Take Profit:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        take_profit_var = tk.DoubleVar(value=current_price * 1.04)
        ttk.Entry(params_frame, textvariable=take_profit_var, width=15).grid(row=3, column=1, padx=5, pady=5)

        # Trailing stop option
        trailing_var = tk.BooleanVar()
        ttk.Checkbutton(params_frame, text="Add Trailing Stop",
                       variable=trailing_var).grid(row=4, column=0, columnspan=2, pady=5)

        # Buttons frame
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        def execute_trade():
            try:
                # Place bracket order
                order_id = self.alpaca_client.place_bracket_order(
                    symbol=symbol,
                    qty=quantity_var.get(),
                    side='buy',
                    stop_loss_price=stop_loss_var.get(),
                    take_profit_price=take_profit_var.get()
                )

                if order_id:
                    # Add trailing stop if requested
                    if trailing_var.get():
                        atr = self.trailing_stop_manager.calculate_atr(symbol)
                        self.trailing_stop_manager.add_trailing_stop(
                            symbol=symbol,
                            order_id=order_id,
                            entry_price=entry_price_var.get(),
                            quantity=quantity_var.get(),
                            stop_type='atr',
                            atr_value=atr
                        )

                    self.log_message(f"Manual trade placed for {symbol} - Order ID: {order_id}")
                    dialog.destroy()
                else:
                    messagebox.showerror("Error", "Failed to place trade")

            except Exception as e:
                messagebox.showerror("Error", f"Trade error: {e}")

        ttk.Button(buttons_frame, text="Execute Trade", command=execute_trade).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def quick_trade_selected(self):
        """Quick trade with default parameters"""
        selection = self.enhanced_signals_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a signal first")
            return

        item = selection[0]
        symbol = self.enhanced_signals_tree.item(item, 'values')[0]

        # Use default trade parameters
        current_price = self.alpaca_client.get_current_price(symbol)
        if not current_price:
            messagebox.showerror("Error", f"Could not get current price for {symbol}")
            return

        # Calculate default parameters
        quantity = 100  # Default quantity
        stop_loss = current_price * 0.98  # 2% stop loss
        take_profit = current_price * 1.04  # 4% take profit

        # Confirm trade
        msg = (f"Quick Trade {symbol}?\n\n"
               f"Quantity: {quantity}\n"
               f"Entry: ${current_price:.2f}\n"
               f"Stop Loss: ${stop_loss:.2f}\n"
               f"Take Profit: ${take_profit:.2f}")

        if messagebox.askyesno("Confirm Quick Trade", msg):
            def trade_thread():
                try:
                    order_id = self.alpaca_client.place_bracket_order(
                        symbol=symbol,
                        qty=quantity,
                        side='buy',
                        stop_loss_price=stop_loss,
                        take_profit_price=take_profit
                    )

                    if order_id:
                        self.root.after(0, lambda: self.log_message(f"Quick trade placed for {symbol} - Order ID: {order_id}"))
                    else:
                        self.root.after(0, lambda: messagebox.showerror("Error", "Failed to place quick trade"))

                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Quick trade error: {e}"))

            threading.Thread(target=trade_thread, daemon=True).start()

    def refresh_trailing_stops(self):
        """Refresh trailing stops display"""
        trailing_stops = self.trailing_stop_manager.get_all_trailing_stops()

        # Clear existing items
        for item in self.trailing_stops_tree.get_children():
            self.trailing_stops_tree.delete(item)

        # Add trailing stops
        for symbol, stop_info in trailing_stops.items():
            current_price = self.alpaca_client.get_current_price(symbol) or 0
            updated_str = stop_info['last_updated'].strftime('%H:%M:%S')

            values = (
                symbol,
                f"${stop_info['entry_price']:.2f}",
                f"${current_price:.2f}",
                f"${stop_info['current_stop']:.2f}",
                f"${stop_info['highest_price']:.2f}",
                stop_info['stop_type'],
                updated_str
            )

            self.trailing_stops_tree.insert('', tk.END, values=values)

    def add_trailing_stop_dialog(self):
        """Open dialog to add trailing stop"""
        # Get active positions
        active_trades = self.trade_manager.get_active_trades()

        if not active_trades:
            messagebox.showinfo("Info", "No active positions to add trailing stops")
            return

        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Add Trailing Stop")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Symbol selection
        ttk.Label(dialog, text="Select Position:").pack(pady=5)
        symbol_var = tk.StringVar()
        symbol_combo = ttk.Combobox(dialog, textvariable=symbol_var, values=list(active_trades.keys()))
        symbol_combo.pack(pady=5)

        # Stop type
        ttk.Label(dialog, text="Stop Type:").pack(pady=5)
        stop_type_var = tk.StringVar(value="atr")
        ttk.Radiobutton(dialog, text="ATR-based", variable=stop_type_var, value="atr").pack()
        ttk.Radiobutton(dialog, text="Percentage-based", variable=stop_type_var, value="percentage").pack()

        def add_stop():
            symbol = symbol_var.get()
            if not symbol:
                messagebox.showwarning("Warning", "Please select a position")
                return

            trade_info = active_trades[symbol]
            atr = self.trailing_stop_manager.calculate_atr(symbol) if stop_type_var.get() == "atr" else None

            success = self.trailing_stop_manager.add_trailing_stop(
                symbol=symbol,
                order_id=trade_info['order_id'],
                entry_price=trade_info['entry_price'],
                quantity=trade_info['quantity'],
                stop_type=stop_type_var.get(),
                atr_value=atr
            )

            if success:
                self.log_message(f"Trailing stop added for {symbol}")
                dialog.destroy()
                self.refresh_trailing_stops()
            else:
                messagebox.showerror("Error", "Failed to add trailing stop")

        ttk.Button(dialog, text="Add Stop", command=add_stop).pack(pady=10)
        ttk.Button(dialog, text="Cancel", command=dialog.destroy).pack()

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()
