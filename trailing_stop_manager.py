"""
Trailing Stop Manager for Enhanced TTM Squeeze Trading
Implements ATR-based and percentage-based trailing stops
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import threading
import time

from alpaca_client import AlpacaClient
from config import config

class TrailingStopManager:
    """Manages trailing stops for active positions"""
    
    def __init__(self, alpaca_client: AlpacaClient):
        """Initialize trailing stop manager"""
        self.alpaca_client = alpaca_client
        self.logger = logging.getLogger(__name__)
        
        # Trailing stop settings
        self.atr_multiplier = config.get("trailing_stop.atr_multiplier", 2.0)
        self.percentage_trail = config.get("trailing_stop.percentage", 0.02)  # 2%
        self.update_frequency = config.get("trailing_stop.update_frequency", 60)  # seconds
        
        # Active trailing stops
        self.trailing_stops = {}  # symbol -> stop_info
        self.running = False
        self.monitor_thread = None
        
        self.logger.info("Trailing Stop Manager initialized")
    
    def add_trailing_stop(self, 
                         symbol: str, 
                         order_id: str,
                         entry_price: float,
                         quantity: int,
                         stop_type: str = 'atr',
                         atr_value: Optional[float] = None) -> bool:
        """
        Add a trailing stop for a position
        
        Args:
            symbol: Stock symbol
            order_id: Original order ID
            entry_price: Entry price of position
            quantity: Number of shares
            stop_type: 'atr' or 'percentage'
            atr_value: ATR value for ATR-based stops
        """
        try:
            current_price = self.alpaca_client.get_current_price(symbol)
            if not current_price:
                self.logger.error(f"Cannot get current price for {symbol}")
                return False
            
            # Calculate initial stop level
            if stop_type == 'atr' and atr_value:
                stop_distance = atr_value * self.atr_multiplier
            else:
                stop_distance = current_price * self.percentage_trail
            
            initial_stop = current_price - stop_distance
            
            stop_info = {
                'symbol': symbol,
                'order_id': order_id,
                'entry_price': entry_price,
                'quantity': quantity,
                'stop_type': stop_type,
                'atr_value': atr_value,
                'current_stop': initial_stop,
                'highest_price': current_price,
                'created_time': datetime.now(),
                'last_updated': datetime.now()
            }
            
            self.trailing_stops[symbol] = stop_info
            
            self.logger.info(
                f"Added trailing stop for {symbol}: "
                f"Entry=${entry_price:.2f}, Stop=${initial_stop:.2f}, "
                f"Type={stop_type}"
            )
            
            # Start monitoring if not already running
            if not self.running:
                self.start_monitoring()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding trailing stop for {symbol}: {e}")
            return False
    
    def update_trailing_stops(self):
        """Update all trailing stops based on current prices"""
        if not self.trailing_stops:
            return
        
        symbols_to_remove = []
        
        for symbol, stop_info in self.trailing_stops.items():
            try:
                current_price = self.alpaca_client.get_current_price(symbol)
                if not current_price:
                    continue
                
                # Check if position still exists
                position = self.alpaca_client.get_position(symbol)
                if not position:
                    self.logger.info(f"Position closed for {symbol}, removing trailing stop")
                    symbols_to_remove.append(symbol)
                    continue
                
                # Update highest price seen
                if current_price > stop_info['highest_price']:
                    stop_info['highest_price'] = current_price
                
                # Calculate new stop level
                if stop_info['stop_type'] == 'atr' and stop_info['atr_value']:
                    stop_distance = stop_info['atr_value'] * self.atr_multiplier
                else:
                    stop_distance = current_price * self.percentage_trail
                
                new_stop = stop_info['highest_price'] - stop_distance
                
                # Only update if new stop is higher (trailing up)
                if new_stop > stop_info['current_stop']:
                    old_stop = stop_info['current_stop']
                    stop_info['current_stop'] = new_stop
                    stop_info['last_updated'] = datetime.now()
                    
                    self.logger.info(
                        f"Updated trailing stop for {symbol}: "
                        f"${old_stop:.2f} -> ${new_stop:.2f} "
                        f"(Price: ${current_price:.2f})"
                    )
                
                # Check if stop should be triggered
                if current_price <= stop_info['current_stop']:
                    self.logger.info(
                        f"Trailing stop triggered for {symbol}: "
                        f"Price=${current_price:.2f} <= Stop=${stop_info['current_stop']:.2f}"
                    )
                    
                    # Execute stop order
                    if self._execute_stop_order(symbol, stop_info):
                        symbols_to_remove.append(symbol)
                
            except Exception as e:
                self.logger.error(f"Error updating trailing stop for {symbol}: {e}")
        
        # Remove completed stops
        for symbol in symbols_to_remove:
            self.remove_trailing_stop(symbol)
    
    def _execute_stop_order(self, symbol: str, stop_info: Dict[str, Any]) -> bool:
        """Execute stop order when trailing stop is triggered"""
        try:
            # Place market sell order
            order_id = self.alpaca_client.place_order(
                symbol=symbol,
                qty=stop_info['quantity'],
                side='sell',
                type='market'
            )
            
            if order_id:
                self.logger.info(
                    f"Stop order executed for {symbol}: "
                    f"Order ID {order_id}, Quantity: {stop_info['quantity']}"
                )
                return True
            else:
                self.logger.error(f"Failed to execute stop order for {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing stop order for {symbol}: {e}")
            return False
    
    def remove_trailing_stop(self, symbol: str):
        """Remove trailing stop for a symbol"""
        if symbol in self.trailing_stops:
            del self.trailing_stops[symbol]
            self.logger.info(f"Removed trailing stop for {symbol}")
    
    def get_trailing_stop_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get trailing stop information for a symbol"""
        return self.trailing_stops.get(symbol)
    
    def get_all_trailing_stops(self) -> Dict[str, Dict[str, Any]]:
        """Get all active trailing stops"""
        return self.trailing_stops.copy()
    
    def start_monitoring(self):
        """Start the trailing stop monitoring thread"""
        if self.running:
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Started trailing stop monitoring")
    
    def stop_monitoring(self):
        """Stop the trailing stop monitoring"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Stopped trailing stop monitoring")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                if self.trailing_stops:
                    self.update_trailing_stops()
                
                # Sleep until next update
                time.sleep(self.update_frequency)
                
            except Exception as e:
                self.logger.error(f"Error in trailing stop monitor loop: {e}")
                time.sleep(self.update_frequency)
    
    def display_trailing_stops(self):
        """Display current trailing stops in text format"""
        if not self.trailing_stops:
            print("\n📊 No active trailing stops")
            return
        
        print(f"\n📊 ACTIVE TRAILING STOPS ({len(self.trailing_stops)})")
        print("-" * 80)
        print(f"{'Symbol':<8} {'Entry':<8} {'Current':<8} {'Stop':<8} {'High':<8} {'Type':<6} {'Updated':<12}")
        print("-" * 80)
        
        for symbol, stop_info in self.trailing_stops.items():
            current_price = self.alpaca_client.get_current_price(symbol) or 0
            updated_str = stop_info['last_updated'].strftime('%H:%M:%S')
            
            print(f"{symbol:<8} ${stop_info['entry_price']:<7.2f} ${current_price:<7.2f} "
                  f"${stop_info['current_stop']:<7.2f} ${stop_info['highest_price']:<7.2f} "
                  f"{stop_info['stop_type']:<6} {updated_str:<12}")
        
        print("-" * 80)
    
    def calculate_atr(self, symbol: str, period: int = 14) -> Optional[float]:
        """Calculate ATR for a symbol"""
        try:
            # Get historical data
            df = self.alpaca_client.get_historical_data(
                symbol, 
                timeframe='1Day', 
                limit=period + 10
            )
            
            if df is None or len(df) < period:
                return None
            
            # Calculate True Range
            df['tr1'] = df['High'] - df['Low']
            df['tr2'] = abs(df['High'] - df['Close'].shift())
            df['tr3'] = abs(df['Low'] - df['Close'].shift())
            df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            
            # Calculate ATR
            atr = df['tr'].rolling(window=period).mean().iloc[-1]
            
            return atr
            
        except Exception as e:
            self.logger.error(f"Error calculating ATR for {symbol}: {e}")
            return None
