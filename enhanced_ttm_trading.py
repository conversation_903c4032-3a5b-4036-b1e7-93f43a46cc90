"""
Enhanced TTM Squeeze Trading Application Launcher
Practical trading interface with enhanced momentum detection
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('enhanced_trading.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main application launcher"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("ENHANCED TTM SQUEEZE TRADING APPLICATION")
    print("=" * 60)
    print("Features:")
    print("✅ Enhanced TTM Squeeze Detection (Red-to-Yellow Phase)")
    print("✅ Manual Trading Interface")
    print("✅ Trailing Stop Management")
    print("✅ Real-time Signal Scanning")
    print("✅ Alpaca Markets Integration")
    print("=" * 60)
    
    try:
        # Check if GUI or console mode
        mode = input("\nSelect mode:\n1. GUI Application\n2. Console Trading Interface\n3. Test Enhanced Signals\nEnter choice (1-3): ").strip()
        
        if mode == '1':
            # Launch GUI application
            print("\n🚀 Launching GUI Application...")
            from gui.main_window import MainWindow
            app = MainWindow()
            app.run()
            
        elif mode == '2':
            # Launch console trading interface
            print("\n🚀 Launching Console Trading Interface...")
            from test_enhanced_ttm_squeeze import TTMSqueezeTrader
            trader = TTMSqueezeTrader()
            trader.run_trading_session()
            
        elif mode == '3':
            # Test enhanced signals
            print("\n🚀 Testing Enhanced TTM Squeeze Signals...")
            test_enhanced_signals()
            
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\n👋 Application terminated by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"❌ Error: {e}")

def test_enhanced_signals():
    """Test enhanced TTM Squeeze signals on sample stocks"""
    from test_enhanced_ttm_squeeze import TTMSqueezeTrader
    
    # Test symbols
    test_symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'SPY']
    
    print(f"\n📊 Testing Enhanced TTM Squeeze on {len(test_symbols)} symbols...")
    print("-" * 60)
    
    try:
        trader = TTMSqueezeTrader()
        
        # Override watchlist for testing
        trader.watchlist = test_symbols
        
        # Scan for signals
        signals = trader.scan_for_signals()
        
        # Display results
        trader.display_signals(signals)
        
        if signals:
            print(f"\n✅ Found {len(signals)} active signals!")
            print("\nSignal Details:")
            print("-" * 50)
            
            for symbol, signal in signals.items():
                print(f"\n{symbol}:")
                print(f"  Price: ${signal['current_price']:.2f}")
                print(f"  Enhanced Signal: {signal['enhanced_early_signal']}")
                print(f"  Yellow Phase: {signal['yellow_phase']}")
                print(f"  Red Phase: {signal['red_phase']}")
                print(f"  Momentum: {signal['momentum']:.4f}")
                print(f"  Squeeze Count: {signal['squeeze_count']} bars")
                print(f"  Squeeze Strength: {signal['squeeze_strength']:.3f}")
        else:
            print("\n❌ No active TTM Squeeze signals found")
            print("This is normal - TTM Squeeze setups are relatively rare")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def show_configuration_help():
    """Show configuration help"""
    print("\n📋 CONFIGURATION HELP")
    print("-" * 30)
    print("1. Ensure your Alpaca API credentials are configured in config.json")
    print("2. Update watchlist.txt with symbols you want to monitor")
    print("3. Adjust TTM Squeeze parameters in the settings if needed")
    print("4. Configure trailing stop settings for risk management")
    print("\nDefault TTM Squeeze Parameters:")
    print("- Bollinger Bands: 20-period, 2.0 std dev")
    print("- Keltner Channels: 20-period, 1.5 ATR")
    print("- Minimum Squeeze: 6 bars")
    print("- Momentum: 20-period linear regression slope")
    print("\nEnhanced Detection Features:")
    print("- Red Phase: 4+ bars of decreasing negative momentum")
    print("- Yellow Phase: 2+ bars of rising negative momentum")
    print("- Enhanced Signal: Triggers during yellow phase transition")

def check_dependencies():
    """Check if all required dependencies are available"""
    required_modules = [
        'pandas', 'numpy', 'alpaca_trade_api', 'tkinter'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Missing required modules: {', '.join(missing_modules)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    return True

def show_help():
    """Show help information"""
    print("\n📖 ENHANCED TTM SQUEEZE TRADING HELP")
    print("=" * 50)
    print("\n🎯 WHAT IS TTM SQUEEZE?")
    print("TTM Squeeze identifies periods of low volatility (squeeze) that often")
    print("precede significant price movements (breakouts).")
    print("\n🚀 ENHANCED DETECTION:")
    print("Our enhanced algorithm detects momentum transitions BEFORE they turn")
    print("positive, providing earlier entry signals during the yellow phase.")
    print("\n📊 SIGNAL TYPES:")
    print("🚀 ENHANCED - Early signal during yellow phase transition")
    print("⚡ EARLY    - Regular early signal detection")
    print("🟡 YELLOW   - Rising but negative momentum phase")
    print("🔴 RED      - Decreasing negative momentum phase")
    print("⏳ SQUEEZE  - Active squeeze condition")
    print("\n💼 TRADING FEATURES:")
    print("- Manual trading with custom parameters")
    print("- Automatic trailing stops (ATR or percentage-based)")
    print("- Real-time signal scanning")
    print("- Risk management tools")
    print("- Performance tracking")
    
    show_configuration_help()

if __name__ == "__main__":
    # Check for help flag
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
        sys.exit(0)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Run main application
    main()
