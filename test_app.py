"""
Test script for TTM Squeeze Trading Application
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ttm_squeeze_detector():
    """Test TTM Squeeze detection logic"""
    print("Testing TTM Squeeze Detector...")
    
    from ttm_squeeze import TTMSqueezeDetector
    
    # Create sample data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5min')
    np.random.seed(42)
    
    # Generate realistic OHLCV data
    base_price = 100
    prices = []
    current_price = base_price
    
    for i in range(100):
        # Random walk with some volatility
        change = np.random.normal(0, 0.5)
        current_price += change
        
        # Generate OHLC from current price
        high = current_price + abs(np.random.normal(0, 0.3))
        low = current_price - abs(np.random.normal(0, 0.3))
        open_price = current_price + np.random.normal(0, 0.1)
        close_price = current_price + np.random.normal(0, 0.1)
        volume = np.random.randint(1000, 10000)
        
        prices.append({
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close_price,
            'Volume': volume
        })
    
    df = pd.DataFrame(prices, index=dates)
    
    # Test TTM Squeeze detector
    detector = TTMSqueezeDetector()
    df_with_indicators = detector.calculate_indicators(df)
    
    print(f"✓ Generated {len(df)} bars of test data")
    print(f"✓ Calculated indicators successfully")
    
    # Check if we have the expected columns
    expected_cols = ['sma', 'upper_BB', 'lower_BB', 'upper_KC', 'lower_KC', 
                     'squeeze_on', 'momentum', 'squeeze_count', 'early_signal']
    
    for col in expected_cols:
        if col in df_with_indicators.columns:
            print(f"✓ Column '{col}' present")
        else:
            print(f"✗ Column '{col}' missing")
    
    # Get signal information
    signal_info = detector.get_current_signal(df_with_indicators)
    print(f"✓ Signal detection completed")
    print(f"  - Has signal: {signal_info['has_signal']}")
    print(f"  - Squeeze active: {signal_info['squeeze_active']}")
    print(f"  - Squeeze count: {signal_info['squeeze_count']}")
    print(f"  - Current price: ${signal_info['current_price']:.2f}")
    
    return True

def test_config():
    """Test configuration management"""
    print("\nTesting Configuration...")
    
    from config import config
    
    # Test basic config access
    api_key = config.get("alpaca.api_key")
    print(f"✓ Alpaca API key configured: {'Yes' if api_key else 'No'}")
    
    # Test watchlist loading
    watchlist = config.load_watchlist()
    print(f"✓ Watchlist loaded with {len(watchlist)} symbols")
    
    # Test credential validation
    has_creds = config.validate_alpaca_credentials()
    print(f"✓ Alpaca credentials valid: {has_creds}")
    
    return True

def test_alpaca_client():
    """Test Alpaca client (without making actual API calls)"""
    print("\nTesting Alpaca Client...")
    
    from alpaca_client import AlpacaClient
    
    client = AlpacaClient()
    print(f"✓ Alpaca client initialized")
    print(f"✓ Connection status: {'Connected' if client.is_connected() else 'Not connected'}")
    
    if client.is_connected():
        print("✓ Alpaca client is ready for trading")
    else:
        print("⚠ Alpaca client not connected (check credentials)")
    
    return True

def test_trade_manager():
    """Test trade manager initialization"""
    print("\nTesting Trade Manager...")
    
    from trade_manager import TradeManager
    
    trade_manager = TradeManager()
    print("✓ Trade manager initialized")
    
    # Test performance summary with no trades
    performance = trade_manager.get_performance_summary()
    print(f"✓ Performance summary: {performance['total_trades']} total trades")
    
    return True

def test_scanner():
    """Test market scanner initialization"""
    print("\nTesting Market Scanner...")
    
    from scanner import MarketScanner
    
    scanner = MarketScanner()
    print("✓ Market scanner initialized")
    
    status = scanner.get_scanner_status()
    print(f"✓ Scanner status: Running={status['is_running']}, Watchlist={status['watchlist_size']} symbols")
    
    return True

def main():
    """Run all tests"""
    print("TTM Squeeze Trading Application - Test Suite")
    print("=" * 50)
    
    tests = [
        test_config,
        test_ttm_squeeze_detector,
        test_alpaca_client,
        test_trade_manager,
        test_scanner
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED\n")
            else:
                failed += 1
                print("✗ FAILED\n")
        except Exception as e:
            failed += 1
            print(f"✗ FAILED: {e}\n")
    
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The application is ready to use.")
        print("\nTo start the application, run:")
        print("  python main.py")
        print("  or")
        print("  run_app.bat")
    else:
        print("⚠ Some tests failed. Please check the configuration and dependencies.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
