"""
TTM Squeeze Pattern Detection
Implements the exact TTM Squeeze detection logic as provided
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional

class TTMSqueezeDetector:
    """TTM Squeeze pattern detection using Bollinger Bands and Keltner Channels"""
    
    def __init__(self, 
                 bb_length: int = 20,
                 bb_multiplier: float = 2.0,
                 kc_length: int = 20,
                 kc_multiplier: float = 1.5,
                 min_squeeze_bars: int = 6,
                 momentum_length: int = 20):
        """
        Initialize TTM Squeeze detector with parameters
        
        Args:
            bb_length: Bollinger Bands period
            bb_multiplier: Bollinger Bands standard deviation multiplier
            kc_length: Keltner Channel period
            kc_multiplier: Keltner Channel ATR multiplier
            min_squeeze_bars: Minimum consecutive squeeze bars for signal
            momentum_length: Period for momentum calculation
        """
        self.bb_length = bb_length
        self.bb_multiplier = bb_multiplier
        self.kc_length = kc_length
        self.kc_multiplier = kc_multiplier
        self.min_squeeze_bars = min_squeeze_bars
        self.momentum_length = momentum_length
        
        self.logger = logging.getLogger(__name__)
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all TTM Squeeze indicators
        
        Args:
            df: DataFrame with OHLCV data (columns: Open, High, Low, Close, Volume)
            
        Returns:
            DataFrame with all indicators added
        """
        if df is None or df.empty:
            self.logger.warning("Empty dataframe provided to TTM Squeeze calculator")
            return df
        
        # Ensure we have required columns
        required_cols = ['Open', 'High', 'Low', 'Close']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"Missing required columns. Need: {required_cols}")
            return df
        
        df = df.copy()
        
        # Calculate SMA
        df['sma'] = df['Close'].rolling(window=self.bb_length).mean()
        
        # Calculate standard deviation
        df['stddev'] = df['Close'].rolling(window=self.bb_length).std()
        
        # Calculate Bollinger Bands
        df['upper_BB'] = df['sma'] + self.bb_multiplier * df['stddev']
        df['lower_BB'] = df['sma'] - self.bb_multiplier * df['stddev']
        
        # Calculate True Range
        df['tr0'] = abs(df['High'] - df['Low'])
        df['tr1'] = abs(df['High'] - df['Close'].shift())
        df['tr2'] = abs(df['Low'] - df['Close'].shift())
        df['tr'] = df[['tr0', 'tr1', 'tr2']].max(axis=1)
        
        # Calculate ATR
        df['atr'] = df['tr'].rolling(window=self.kc_length).mean()
        
        # Calculate Keltner Channels
        df['upper_KC'] = df['sma'] + self.kc_multiplier * df['atr']
        df['lower_KC'] = df['sma'] - self.kc_multiplier * df['atr']
        
        # Determine squeeze condition
        df['squeeze_on'] = (df['lower_BB'] > df['lower_KC']) & (df['upper_BB'] < df['upper_KC'])
        
        # Calculate momentum histogram using linear regression slope
        df['momentum'] = self._calculate_momentum(df['Close'])
        
        # Calculate squeeze count and enhanced signals
        df['squeeze_count'] = self._calculate_squeeze_count(df['squeeze_on'])

        # Enhanced momentum phase analysis
        df = self._analyze_momentum_phases(df)

        # Calculate both original and enhanced early signals
        df['early_signal'] = self._calculate_early_signal(df)
        df['enhanced_early_signal'] = self._calculate_enhanced_early_signal(df)

        # Combined signal prioritizing enhanced detection
        df['ttm_signal'] = df['enhanced_early_signal'] | df['early_signal']

        return df
    
    def _calculate_momentum(self, close_series: pd.Series) -> pd.Series:
        """Calculate momentum using linear regression slope"""
        def linreg_slope(series, n):
            slopes = []
            for i in range(n, len(series)):
                y = series.iloc[i-n:i].values
                x = np.arange(n)
                if len(y) == n:  # Ensure we have enough data
                    slope, _ = np.polyfit(x, y, 1)
                    slopes.append(slope)
                else:
                    slopes.append(np.nan)
            return pd.Series(slopes, index=series.index[n:])
        
        return linreg_slope(close_series, self.momentum_length)
    
    def _calculate_squeeze_count(self, squeeze_series: pd.Series) -> pd.Series:
        """Calculate rolling count of consecutive squeeze bars"""
        squeeze_count = pd.Series(0, index=squeeze_series.index)
        current_count = 0
        
        for i, is_squeeze in enumerate(squeeze_series):
            if pd.isna(is_squeeze):
                squeeze_count.iloc[i] = 0
                current_count = 0
            elif is_squeeze:
                current_count += 1
                squeeze_count.iloc[i] = current_count
            else:
                current_count = 0
                squeeze_count.iloc[i] = 0
        
        return squeeze_count
    
    def _calculate_early_signal(self, df: pd.DataFrame) -> pd.Series:
        """
        Enhanced early signal detection for TTM Squeeze breakout anticipation

        Detects the transition from red histogram (decreasing negative momentum)
        to yellow histogram (rising but still negative momentum) phase
        """
        early_signal = pd.Series(False, index=df.index)

        if len(df) < 10:  # Need sufficient data for pattern analysis
            return early_signal

        # Calculate momentum trend indicators
        momentum_change = df['momentum'].diff()

        # Phase 1: Red Histogram Phase Detection
        # Momentum is negative and decreasing for 4-5 consecutive bars
        red_phase = self._detect_red_histogram_phase(df['momentum'], momentum_change)

        # Phase 2: Yellow Transition Phase Detection
        # Momentum is still negative but rising for 2+ consecutive bars
        yellow_phase = self._detect_yellow_transition_phase(df['momentum'], momentum_change)

        # Enhanced early signal conditions
        condition1 = df['squeeze_count'] >= self.min_squeeze_bars  # Squeeze active for 6+ bars
        condition2 = df['squeeze_on']  # Current squeeze is active
        condition3 = yellow_phase  # In yellow transition phase
        condition4 = df['momentum'] < 0  # Momentum still negative (not yet positive)
        condition5 = momentum_change > 0  # Momentum is rising
        condition6 = momentum_change.shift(1) > 0  # Momentum was rising previous bar too

        # Additional confirmation: ensure we had a proper red phase recently
        red_phase_recent = red_phase.rolling(window=8).sum() >= 1

        # Combine all conditions for enhanced early signal
        early_signal = (condition1 & condition2 & condition3 & condition4 &
                       condition5 & condition6 & red_phase_recent)

        return early_signal

    def _detect_red_histogram_phase(self, momentum: pd.Series, momentum_change: pd.Series) -> pd.Series:
        """
        Detect red histogram phase: negative and decreasing momentum for 4-5 bars
        """
        red_phase = pd.Series(False, index=momentum.index)

        # Conditions for red phase
        negative_momentum = momentum < 0
        decreasing_momentum = momentum_change < 0

        # Count consecutive decreasing negative bars
        consecutive_red = pd.Series(0, index=momentum.index)
        count = 0

        for i in range(len(momentum)):
            if negative_momentum.iloc[i] and decreasing_momentum.iloc[i]:
                count += 1
            else:
                count = 0
            consecutive_red.iloc[i] = count

        # Red phase is active when we have 4+ consecutive decreasing negative bars
        red_phase = consecutive_red >= 4

        return red_phase

    def _detect_yellow_transition_phase(self, momentum: pd.Series, momentum_change: pd.Series) -> pd.Series:
        """
        Detect yellow transition phase: negative but rising momentum for 2+ bars
        """
        yellow_phase = pd.Series(False, index=momentum.index)

        # Conditions for yellow phase
        negative_momentum = momentum < 0
        rising_momentum = momentum_change > 0

        # Count consecutive rising negative bars
        consecutive_yellow = pd.Series(0, index=momentum.index)
        count = 0

        for i in range(len(momentum)):
            if negative_momentum.iloc[i] and rising_momentum.iloc[i]:
                count += 1
            else:
                count = 0
            consecutive_yellow.iloc[i] = count

        # Yellow phase is active when we have 2+ consecutive rising negative bars
        yellow_phase = consecutive_yellow >= 2

        return yellow_phase

    def _analyze_momentum_phases(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Analyze momentum phases and add phase indicators to dataframe
        """
        momentum_change = df['momentum'].diff()

        # Add phase detection columns
        df['red_phase'] = self._detect_red_histogram_phase(df['momentum'], momentum_change)
        df['yellow_phase'] = self._detect_yellow_transition_phase(df['momentum'], momentum_change)

        # Add momentum strength indicators
        df['momentum_strength'] = abs(df['momentum'])
        df['momentum_acceleration'] = momentum_change.diff()

        # Detect momentum divergence (price vs momentum)
        df['price_momentum_divergence'] = self._detect_momentum_divergence(df)

        return df

    def _detect_momentum_divergence(self, df: pd.DataFrame) -> pd.Series:
        """
        Detect divergence between price action and momentum
        """
        divergence = pd.Series(False, index=df.index)

        if len(df) < 20:
            return divergence

        # Calculate price and momentum trends over last 10 bars
        price_trend = df['Close'].rolling(window=10).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])
        momentum_trend = df['momentum'].rolling(window=10).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])

        # Divergence occurs when price and momentum trends are opposite
        divergence = (price_trend > 0) & (momentum_trend < 0) | (price_trend < 0) & (momentum_trend > 0)

        return divergence

    def _calculate_enhanced_early_signal(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate enhanced early signal with additional momentum analysis
        """
        enhanced_signal = pd.Series(False, index=df.index)

        if len(df) < 15:
            return enhanced_signal

        # Core conditions from original early signal
        base_conditions = df['early_signal']

        # Additional enhancement conditions
        momentum_strength_condition = df['momentum_strength'] > df['momentum_strength'].rolling(window=10).mean()
        acceleration_condition = df['momentum_acceleration'] > 0

        # Volume confirmation (if available)
        volume_condition = True
        if 'Volume' in df.columns:
            avg_volume = df['Volume'].rolling(window=20).mean()
            volume_condition = df['Volume'] > avg_volume * 1.2  # 20% above average

        # Combine all conditions for enhanced signal
        enhanced_signal = (base_conditions &
                          momentum_strength_condition &
                          acceleration_condition &
                          volume_condition)

        return enhanced_signal

    def get_current_signal(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get current TTM Squeeze signal information
        
        Args:
            df: DataFrame with calculated indicators
            
        Returns:
            Dictionary with signal information
        """
        if df is None or df.empty:
            return {
                'has_signal': False,
                'error': 'No data available'
            }
        
        latest = df.iloc[-1]
        
        signal_info = {
            'has_signal': bool(latest.get('ttm_signal', False)),
            'early_signal': bool(latest.get('early_signal', False)),
            'enhanced_early_signal': bool(latest.get('enhanced_early_signal', False)),
            'squeeze_active': bool(latest.get('squeeze_on', False)),
            'squeeze_count': int(latest.get('squeeze_count', 0)),
            'momentum': float(latest.get('momentum', 0)),
            'momentum_strength': float(latest.get('momentum_strength', 0)),
            'red_phase': bool(latest.get('red_phase', False)),
            'yellow_phase': bool(latest.get('yellow_phase', False)),
            'current_price': float(latest.get('Close', 0)),
            'sma': float(latest.get('sma', 0)),
            'upper_bb': float(latest.get('upper_BB', 0)),
            'lower_bb': float(latest.get('lower_BB', 0)),
            'upper_kc': float(latest.get('upper_KC', 0)),
            'lower_kc': float(latest.get('lower_KC', 0)),
            'atr': float(latest.get('atr', 0)),
            'timestamp': latest.name if hasattr(latest, 'name') else None
        }
        
        # Calculate squeeze strength (how tight the squeeze is)
        if not pd.isna(latest.get('upper_BB')) and not pd.isna(latest.get('upper_KC')):
            bb_width = latest['upper_BB'] - latest['lower_BB']
            kc_width = latest['upper_KC'] - latest['lower_KC']
            signal_info['squeeze_strength'] = (kc_width - bb_width) / kc_width if kc_width > 0 else 0
        else:
            signal_info['squeeze_strength'] = 0
        
        return signal_info
    
    def scan_for_signals(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, Any]]:
        """
        Scan multiple symbols for TTM Squeeze signals
        
        Args:
            data_dict: Dictionary with symbol as key and OHLCV DataFrame as value
            
        Returns:
            Dictionary with symbol as key and signal info as value
        """
        signals = {}
        
        for symbol, df in data_dict.items():
            try:
                # Calculate indicators
                df_with_indicators = self.calculate_indicators(df)
                
                # Get signal information
                signal_info = self.get_current_signal(df_with_indicators)
                signal_info['symbol'] = symbol
                
                # Only include symbols with active signals or squeezes
                if signal_info['has_signal'] or signal_info['squeeze_active']:
                    signals[symbol] = signal_info
                    
            except Exception as e:
                self.logger.error(f"Error processing {symbol}: {e}")
                continue
        
        return signals
