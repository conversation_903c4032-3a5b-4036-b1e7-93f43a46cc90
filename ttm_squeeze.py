"""
TTM Squeeze Pattern Detection
Implements the exact TTM Squeeze detection logic as provided
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional

class TTMSqueezeDetector:
    """TTM Squeeze pattern detection using Bollinger Bands and Keltner Channels"""
    
    def __init__(self, 
                 bb_length: int = 20,
                 bb_multiplier: float = 2.0,
                 kc_length: int = 20,
                 kc_multiplier: float = 1.5,
                 min_squeeze_bars: int = 6,
                 momentum_length: int = 20):
        """
        Initialize TTM Squeeze detector with parameters
        
        Args:
            bb_length: Bollinger Bands period
            bb_multiplier: Bollinger Bands standard deviation multiplier
            kc_length: Keltner Channel period
            kc_multiplier: Keltner Channel ATR multiplier
            min_squeeze_bars: Minimum consecutive squeeze bars for signal
            momentum_length: Period for momentum calculation
        """
        self.bb_length = bb_length
        self.bb_multiplier = bb_multiplier
        self.kc_length = kc_length
        self.kc_multiplier = kc_multiplier
        self.min_squeeze_bars = min_squeeze_bars
        self.momentum_length = momentum_length
        
        self.logger = logging.getLogger(__name__)
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all TTM Squeeze indicators
        
        Args:
            df: DataFrame with OHLCV data (columns: Open, High, Low, Close, Volume)
            
        Returns:
            DataFrame with all indicators added
        """
        if df is None or df.empty:
            self.logger.warning("Empty dataframe provided to TTM Squeeze calculator")
            return df
        
        # Ensure we have required columns
        required_cols = ['Open', 'High', 'Low', 'Close']
        if not all(col in df.columns for col in required_cols):
            self.logger.error(f"Missing required columns. Need: {required_cols}")
            return df
        
        df = df.copy()
        
        # Calculate SMA
        df['sma'] = df['Close'].rolling(window=self.bb_length).mean()
        
        # Calculate standard deviation
        df['stddev'] = df['Close'].rolling(window=self.bb_length).std()
        
        # Calculate Bollinger Bands
        df['upper_BB'] = df['sma'] + self.bb_multiplier * df['stddev']
        df['lower_BB'] = df['sma'] - self.bb_multiplier * df['stddev']
        
        # Calculate True Range
        df['tr0'] = abs(df['High'] - df['Low'])
        df['tr1'] = abs(df['High'] - df['Close'].shift())
        df['tr2'] = abs(df['Low'] - df['Close'].shift())
        df['tr'] = df[['tr0', 'tr1', 'tr2']].max(axis=1)
        
        # Calculate ATR
        df['atr'] = df['tr'].rolling(window=self.kc_length).mean()
        
        # Calculate Keltner Channels
        df['upper_KC'] = df['sma'] + self.kc_multiplier * df['atr']
        df['lower_KC'] = df['sma'] - self.kc_multiplier * df['atr']
        
        # Determine squeeze condition
        df['squeeze_on'] = (df['lower_BB'] > df['lower_KC']) & (df['upper_BB'] < df['upper_KC'])
        
        # Calculate momentum histogram using linear regression slope
        df['momentum'] = self._calculate_momentum(df['Close'])
        
        # Calculate squeeze count and early signal
        df['squeeze_count'] = self._calculate_squeeze_count(df['squeeze_on'])
        df['early_signal'] = self._calculate_early_signal(df)
        
        return df
    
    def _calculate_momentum(self, close_series: pd.Series) -> pd.Series:
        """Calculate momentum using linear regression slope"""
        def linreg_slope(series, n):
            slopes = []
            for i in range(n, len(series)):
                y = series.iloc[i-n:i].values
                x = np.arange(n)
                if len(y) == n:  # Ensure we have enough data
                    slope, _ = np.polyfit(x, y, 1)
                    slopes.append(slope)
                else:
                    slopes.append(np.nan)
            return pd.Series(slopes, index=series.index[n:])
        
        return linreg_slope(close_series, self.momentum_length)
    
    def _calculate_squeeze_count(self, squeeze_series: pd.Series) -> pd.Series:
        """Calculate rolling count of consecutive squeeze bars"""
        squeeze_count = pd.Series(0, index=squeeze_series.index)
        current_count = 0
        
        for i, is_squeeze in enumerate(squeeze_series):
            if pd.isna(is_squeeze):
                squeeze_count.iloc[i] = 0
                current_count = 0
            elif is_squeeze:
                current_count += 1
                squeeze_count.iloc[i] = current_count
            else:
                current_count = 0
                squeeze_count.iloc[i] = 0
        
        return squeeze_count
    
    def _calculate_early_signal(self, df: pd.DataFrame) -> pd.Series:
        """Calculate early signal based on squeeze duration and momentum"""
        early_signal = pd.Series(False, index=df.index)
        
        # Early detection: prolonged squeeze (6+ bars) and momentum not rising
        condition1 = df['squeeze_count'] >= self.min_squeeze_bars
        condition2 = df['momentum'] <= df['momentum'].shift(1)
        
        early_signal = condition1 & condition2
        
        return early_signal
    
    def get_current_signal(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get current TTM Squeeze signal information
        
        Args:
            df: DataFrame with calculated indicators
            
        Returns:
            Dictionary with signal information
        """
        if df is None or df.empty:
            return {
                'has_signal': False,
                'error': 'No data available'
            }
        
        latest = df.iloc[-1]
        
        signal_info = {
            'has_signal': bool(latest.get('early_signal', False)),
            'squeeze_active': bool(latest.get('squeeze_on', False)),
            'squeeze_count': int(latest.get('squeeze_count', 0)),
            'momentum': float(latest.get('momentum', 0)),
            'current_price': float(latest.get('Close', 0)),
            'sma': float(latest.get('sma', 0)),
            'upper_bb': float(latest.get('upper_BB', 0)),
            'lower_bb': float(latest.get('lower_BB', 0)),
            'upper_kc': float(latest.get('upper_KC', 0)),
            'lower_kc': float(latest.get('lower_KC', 0)),
            'atr': float(latest.get('atr', 0)),
            'timestamp': latest.name if hasattr(latest, 'name') else None
        }
        
        # Calculate squeeze strength (how tight the squeeze is)
        if not pd.isna(latest.get('upper_BB')) and not pd.isna(latest.get('upper_KC')):
            bb_width = latest['upper_BB'] - latest['lower_BB']
            kc_width = latest['upper_KC'] - latest['lower_KC']
            signal_info['squeeze_strength'] = (kc_width - bb_width) / kc_width if kc_width > 0 else 0
        else:
            signal_info['squeeze_strength'] = 0
        
        return signal_info
    
    def scan_for_signals(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, Any]]:
        """
        Scan multiple symbols for TTM Squeeze signals
        
        Args:
            data_dict: Dictionary with symbol as key and OHLCV DataFrame as value
            
        Returns:
            Dictionary with symbol as key and signal info as value
        """
        signals = {}
        
        for symbol, df in data_dict.items():
            try:
                # Calculate indicators
                df_with_indicators = self.calculate_indicators(df)
                
                # Get signal information
                signal_info = self.get_current_signal(df_with_indicators)
                signal_info['symbol'] = symbol
                
                # Only include symbols with active signals or squeezes
                if signal_info['has_signal'] or signal_info['squeeze_active']:
                    signals[symbol] = signal_info
                    
            except Exception as e:
                self.logger.error(f"Error processing {symbol}: {e}")
                continue
        
        return signals
