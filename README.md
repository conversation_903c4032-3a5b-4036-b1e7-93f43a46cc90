# TTM Squeeze Trading Application

A comprehensive desktop trading application that implements TTM Squeeze pattern detection and automated trading using the Alpaca Markets API.

## Features

### Core Functionality
- **Real-time TTM Squeeze Pattern Detection**: Uses Bollinger Bands and Keltner Channels to identify squeeze conditions
- **Automated Market Scanning**: Continuously scans your watchlist every 5 minutes during market hours
- **Integrated Trading**: Direct integration with Alpaca Markets for order execution
- **Risk Management**: Built-in position sizing, stop-loss, and take-profit management
- **Trade Logging**: Comprehensive logging of all trades with performance analytics

### TTM Squeeze Detection Criteria
- Bollinger Bands (20-period SMA, 2.0 multiplier) contained within Keltner Channels (20-period SMA, 1.5 ATR multiplier)
- Squeeze condition persists for 6 or more consecutive bars
- Momentum histogram (linear regression slope) is flat or declining
- Early signal triggers when: `squeeze_count >= 6` AND `momentum <= previous_momentum`

### User Interface
- **Market Scanner Tab**: Real-time display of detected opportunities
- **Positions Tab**: Active trades monitoring and performance summary
- **Settings Tab**: Configurable trading parameters and watchlist management
- **Logs Tab**: Application logs and trade history

## Installation

### Prerequisites
- Python 3.8 or higher
- Alpaca Markets account (paper trading recommended for testing)

### Setup Steps

1. **Clone or download the application files**

2. **Install required dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure API credentials**:
   - Open `config.json`
   - Add your Alpaca API credentials:
     ```json
     {
       "alpaca": {
         "api_key": "YOUR_ALPACA_API_KEY",
         "secret_key": "YOUR_ALPACA_SECRET_KEY",
         "base_url": "https://paper-api.alpaca.markets"
       }
     }
     ```

4. **Customize your watchlist** (optional):
   - Edit `watchlist.txt` to include your preferred symbols
   - One symbol per line, comments start with #

5. **Run the application**:
   ```bash
   python main.py
   ```

## Configuration

### Trading Settings
- **Position Size**: Percentage of account equity per trade (default: 2%)
- **Stop Loss**: Percentage below entry price (default: 2%)
- **Take Profit Ratio**: Risk/reward ratio (default: 2:1)
- **Max Concurrent Trades**: Maximum number of simultaneous positions (default: 5)

### Scanner Settings
- **Scan Interval**: How often to scan the market (default: 5 minutes)
- **Market Hours Only**: Whether to scan only during market hours (default: true)

### TTM Squeeze Parameters
- **Bollinger Bands Length**: 20 periods
- **Bollinger Bands Multiplier**: 2.0
- **Keltner Channel Length**: 20 periods
- **Keltner Channel Multiplier**: 1.5
- **Minimum Squeeze Bars**: 6 consecutive bars

## Usage

### Starting the Scanner
1. Launch the application
2. Go to the "Market Scanner" tab
3. Click "Start Scanner" to begin continuous monitoring
4. Or click "Manual Scan" for one-time scanning

### Placing Trades
1. Review detected opportunities in the scanner table
2. Select a symbol by clicking on it
3. Click "Place Trade" to execute a bracket order
4. Confirm the trade details in the popup dialog

### Monitoring Positions
1. Go to the "Positions" tab
2. View active trades with real-time P&L
3. Monitor account information and performance summary

### Managing Settings
1. Go to the "Settings" tab
2. Adjust trading parameters as needed
3. Click "Save Settings" to apply changes
4. Edit the watchlist and click "Save Watchlist"

## Risk Management

### Built-in Protections
- **Position Sizing**: Automatically calculated based on account equity
- **Stop Loss Orders**: Automatic stop-loss placement with every trade
- **Take Profit Orders**: Automatic profit-taking at predetermined levels
- **Maximum Positions**: Limits concurrent trades to prevent overexposure

### Important Notes
- **Paper Trading**: Start with paper trading to test the system
- **Position Sizing**: Default 2% of equity per trade - adjust based on your risk tolerance
- **Market Hours**: Scanner respects market hours by default
- **Data Quality**: Ensure stable internet connection for reliable data feeds

## File Structure

```
TTM_Squeeze_Trading/
├── main.py                 # Application entry point
├── config.py              # Configuration management
├── config.json            # Application settings
├── ttm_squeeze.py         # TTM Squeeze detection logic
├── alpaca_client.py       # Alpaca API integration
├── scanner.py             # Market scanning engine
├── trade_manager.py       # Trade execution and management
├── watchlist.txt          # Stock symbols to monitor
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── gui/
│   ├── __init__.py
│   └── main_window.py    # Main GUI interface
├── trading.log           # Application logs (created at runtime)
└── trades.csv           # Trade history log (created at runtime)
```

## Logging and Data

### Trade Logging
All trades are logged to `trades.csv` with the following information:
- Entry and exit timestamps
- Symbol, quantity, and prices
- P&L calculations
- Exit reasons (stop-loss, take-profit, manual)
- TTM Squeeze parameters at entry

### Application Logs
Detailed application logs are saved to `trading.log` including:
- Scanner activity and signals found
- Trade execution details
- Error messages and debugging information

## Troubleshooting

### Common Issues

1. **"Alpaca client not connected"**
   - Check your API credentials in `config.json`
   - Ensure you're using the correct base URL (paper vs live)
   - Verify your internet connection

2. **"No data returned for symbol"**
   - Symbol may not be supported by Alpaca
   - Check if the market is open
   - Verify symbol spelling in watchlist

3. **"Cannot place order"**
   - Check account buying power
   - Ensure position limits aren't exceeded
   - Verify market hours for trading

4. **Scanner not finding signals**
   - TTM Squeeze is a specific pattern that may not occur frequently
   - Try expanding your watchlist
   - Check if squeeze parameters are too restrictive

### Getting Help
- Check the logs tab in the application for detailed error messages
- Review `trading.log` file for technical details
- Ensure all dependencies are properly installed

## Disclaimer

This software is for educational and research purposes. Trading involves substantial risk of loss. Past performance does not guarantee future results. Always test with paper trading before using real money. The authors are not responsible for any financial losses incurred through the use of this software.

## License

This project is provided as-is for educational purposes. Please ensure compliance with all applicable financial regulations in your jurisdiction.
