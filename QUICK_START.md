# TTM Squeeze Trading Application - Quick Start Guide

## 🚀 Ready to Use!

Your TTM Squeeze Trading Application is fully configured and ready to use with your Alpaca API credentials.

## ✅ What's Already Set Up

- ✅ **Alpaca API Integration**: Connected to your paper trading account
- ✅ **TTM Squeeze Detection**: Exact algorithm implementation as specified
- ✅ **42 Stock Watchlist**: Pre-loaded with popular stocks and ETFs
- ✅ **Risk Management**: Position sizing, stop-loss, and take-profit controls
- ✅ **Trade Logging**: Automatic CSV logging of all trades
- ✅ **Desktop GUI**: User-friendly interface with real-time updates

## 🎯 How to Start Trading

### 1. Launch the Application
```bash
# Option 1: Run directly
python main.py

# Option 2: Use the batch file (Windows)
run_app.bat
```

### 2. Start Market Scanning
1. Click the **"Market Scanner"** tab
2. Click **"Start Scanner"** to begin continuous monitoring
3. Or click **"Manual Scan"** for one-time scanning

### 3. Place Your First Trade
1. Wait for signals to appear in the opportunities table
2. Select a symbol with a "SIGNAL" status
3. Click **"Place Trade"** to execute a bracket order
4. Confirm the trade details in the popup

### 4. Monitor Your Positions
1. Go to the **"Positions"** tab
2. View real-time P&L for active trades
3. Check your performance summary

## 📊 Understanding the Signals

### TTM Squeeze Conditions
- **Squeeze Active**: Bollinger Bands inside Keltner Channels
- **Squeeze Count**: Number of consecutive squeeze bars (need 6+)
- **Momentum**: Linear regression slope (should be flat/declining)
- **Signal**: Triggers when squeeze ≥ 6 bars AND momentum ≤ previous

### Signal Table Columns
- **Symbol**: Stock ticker
- **Price**: Current stock price
- **Squeeze Count**: Consecutive squeeze bars
- **Momentum**: Current momentum value
- **Strength**: How tight the squeeze is (0-1)
- **Signal**: "SIGNAL" = ready to trade, "SQUEEZE" = building up
- **Last Update**: When data was last refreshed

## ⚙️ Default Settings (Already Configured)

### Trading Parameters
- **Position Size**: 2% of account equity per trade
- **Stop Loss**: 2% below entry price
- **Take Profit**: 2:1 risk/reward ratio (4% above entry)
- **Max Concurrent Trades**: 5 positions maximum

### Scanner Settings
- **Scan Interval**: Every 5 minutes during market hours
- **Market Hours Only**: Yes (respects market schedule)
- **Timeframe**: 5-minute bars for analysis

## 📈 Your Watchlist (42 Symbols)

**Large Cap Tech**: AAPL, MSFT, GOOGL, AMZN, TSLA, META, NVDA, NFLX
**Financial**: JPM, BAC, WFC, GS, MS
**Healthcare**: JNJ, PFE, UNH, ABBV, MRK
**Consumer**: KO, PEP, WMT, HD, DIS
**ETFs**: SPY, QQQ, IWM, XLF, XLK, XLE, XLV, XLI, XLU, XLP
**Growth**: SQ, ROKU, ZOOM, DOCU, CRWD
**High Volatility**: GME, AMC, BB, NOK

## 🛡️ Safety Features

### Built-in Risk Management
- **Paper Trading**: Currently using Alpaca paper account (safe testing)
- **Position Limits**: Maximum 5 concurrent trades
- **Stop Losses**: Automatic stop-loss orders on every trade
- **Take Profits**: Automatic profit-taking at 2:1 ratio

### Trade Logging
- All trades logged to `trades.csv`
- Includes entry/exit prices, P&L, and squeeze parameters
- Application logs in `trading.log`

## 🔧 Customization Options

### Adjust Trading Settings
1. Go to **"Settings"** tab
2. Modify position size, stop loss, take profit ratio
3. Click **"Save Settings"**

### Update Watchlist
1. Go to **"Settings"** tab
2. Edit symbols in the text area (one per line)
3. Click **"Save Watchlist"**

### Change TTM Squeeze Parameters
Edit `config.json` to modify:
- `bb_length`: Bollinger Bands period (default: 20)
- `bb_multiplier`: BB standard deviation multiplier (default: 2.0)
- `kc_length`: Keltner Channel period (default: 20)
- `kc_multiplier`: KC ATR multiplier (default: 1.5)
- `min_squeeze_bars`: Minimum squeeze duration (default: 6)

## 📱 Using the Interface

### Market Scanner Tab
- **Green rows**: Active squeeze conditions
- **Red rows**: Signal ready for trading
- **Double-click**: Select symbol for trading

### Positions Tab
- **Account Info**: Buying power, cash, portfolio value
- **Active Trades**: Real-time P&L monitoring
- **Performance**: Win rate, total P&L, profit factor

### Logs Tab
- Real-time application messages
- Trade execution confirmations
- Error messages and debugging info

## 🎯 Trading Strategy Tips

### Best Practices
1. **Start Small**: Use default 2% position sizing
2. **Be Patient**: TTM Squeeze signals are not frequent but high-quality
3. **Monitor Market Hours**: Scanner works best during active trading
4. **Review Performance**: Check the performance summary regularly

### What to Expect
- **Signal Frequency**: 1-5 signals per day depending on market conditions
- **Hold Time**: Trades typically last hours to days
- **Win Rate**: TTM Squeeze typically has 60-70% win rate
- **Risk/Reward**: 2:1 ratio means profitable even with 50% win rate

## 🚨 Important Notes

### Current Status
- **Paper Trading**: You're using Alpaca's paper trading environment (safe)
- **Real Money**: To switch to live trading, change `base_url` in config.json
- **Market Data**: Real-time data from Alpaca (5-minute bars)

### Before Live Trading
1. Test thoroughly with paper trading
2. Understand the TTM Squeeze pattern
3. Monitor performance for at least a week
4. Adjust position sizing based on your risk tolerance

## 📞 Support

### Files to Check
- `trading.log`: Application logs
- `trades.csv`: Trade history
- `config.json`: All settings

### Common Issues
- **No signals**: TTM Squeeze is selective - be patient
- **Connection errors**: Check internet and API credentials
- **Trade failures**: Verify account buying power

---

## 🎉 You're Ready to Trade!

Your TTM Squeeze Trading Application is fully configured and tested. Start with the scanner to see it in action!

**Happy Trading! 📈**
