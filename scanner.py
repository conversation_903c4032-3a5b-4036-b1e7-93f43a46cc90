"""
Market Scanner for TTM Squeeze Pattern Detection
"""
import threading
import time
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Callable, Optional
import schedule

from config import config
from alpaca_client import AlpacaClient
from ttm_squeeze import TTMSqueezeDetector

class MarketScanner:
    """Market scanner for continuous TTM Squeeze pattern detection"""
    
    def __init__(self, callback: Optional[Callable] = None):
        """
        Initialize market scanner
        
        Args:
            callback: Function to call when new signals are found
        """
        self.logger = logging.getLogger(__name__)
        self.callback = callback
        
        # Initialize components
        self.alpaca_client = AlpacaClient()
        self.squeeze_detector = TTMSqueezeDetector(
            bb_length=config.get("ttm_squeeze.bb_length", 20),
            bb_multiplier=config.get("ttm_squeeze.bb_multiplier", 2.0),
            kc_length=config.get("ttm_squeeze.kc_length", 20),
            kc_multiplier=config.get("ttm_squeeze.kc_multiplier", 1.5),
            min_squeeze_bars=config.get("ttm_squeeze.min_squeeze_bars", 6),
            momentum_length=config.get("ttm_squeeze.momentum_length", 20)
        )
        
        # Scanner state
        self.is_running = False
        self.scan_thread = None
        self.watchlist = config.load_watchlist()
        self.current_signals = {}
        self.last_scan_time = None
        
        # Configuration
        self.scan_interval = config.get("scanning.scan_interval_minutes", 5)
        self.market_hours_only = config.get("scanning.market_hours_only", True)
        
        self.logger.info(f"Scanner initialized with {len(self.watchlist)} symbols")
    
    def start_scanning(self):
        """Start the market scanning process"""
        if self.is_running:
            self.logger.warning("Scanner is already running")
            return
        
        if not self.alpaca_client.is_connected():
            self.logger.error("Cannot start scanner - Alpaca client not connected")
            return
        
        self.is_running = True
        
        # Schedule scanning
        schedule.every(self.scan_interval).minutes.do(self._scan_market)
        
        # Start scanner thread
        self.scan_thread = threading.Thread(target=self._scanner_loop, daemon=True)
        self.scan_thread.start()
        
        self.logger.info(f"Market scanner started - scanning every {self.scan_interval} minutes")
    
    def stop_scanning(self):
        """Stop the market scanning process"""
        self.is_running = False
        schedule.clear()
        
        if self.scan_thread and self.scan_thread.is_alive():
            self.scan_thread.join(timeout=5)
        
        self.logger.info("Market scanner stopped")
    
    def _scanner_loop(self):
        """Main scanner loop running in separate thread"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"Error in scanner loop: {e}")
                time.sleep(5)
    
    def _scan_market(self):
        """Perform market scan for TTM Squeeze patterns"""
        try:
            # Check if we should scan during current market hours
            if self.market_hours_only and not self._should_scan_now():
                self.logger.debug("Skipping scan - outside market hours")
                return
            
            self.logger.info("Starting market scan...")
            scan_start_time = time.time()
            
            # Get market data for all symbols
            market_data = self.alpaca_client.get_multiple_symbols_data(
                self.watchlist,
                timeframe='5Min',
                limit=100  # Get enough data for indicators
            )
            
            if not market_data:
                self.logger.warning("No market data retrieved")
                return
            
            # Scan for TTM Squeeze signals
            signals = self.squeeze_detector.scan_for_signals(market_data)
            
            # Update current signals
            self.current_signals = signals
            self.last_scan_time = datetime.now()
            
            scan_duration = time.time() - scan_start_time
            
            self.logger.info(
                f"Scan completed in {scan_duration:.2f}s - "
                f"Found {len(signals)} signals out of {len(market_data)} symbols"
            )
            
            # Log signal details
            for symbol, signal in signals.items():
                if signal['has_signal']:
                    self.logger.info(
                        f"SIGNAL: {symbol} - Squeeze: {signal['squeeze_count']} bars, "
                        f"Momentum: {signal['momentum']:.4f}, Price: ${signal['current_price']:.2f}"
                    )
            
            # Call callback if provided
            if self.callback:
                try:
                    self.callback(signals)
                except Exception as e:
                    self.logger.error(f"Error in scanner callback: {e}")
            
        except Exception as e:
            self.logger.error(f"Error during market scan: {e}")
    
    def _should_scan_now(self) -> bool:
        """Check if we should scan based on market hours and configuration"""
        if not self.market_hours_only:
            return True
        
        # Check if market is open
        try:
            return self.alpaca_client.is_market_open()
        except Exception as e:
            self.logger.error(f"Error checking market hours: {e}")
            return False
    
    def manual_scan(self) -> Dict[str, Any]:
        """Perform a manual scan and return results immediately"""
        self.logger.info("Performing manual scan...")
        
        try:
            # Get market data
            market_data = self.alpaca_client.get_multiple_symbols_data(
                self.watchlist,
                timeframe='5Min',
                limit=100
            )
            
            if not market_data:
                return {}
            
            # Scan for signals
            signals = self.squeeze_detector.scan_for_signals(market_data)
            
            # Update current signals
            self.current_signals = signals
            self.last_scan_time = datetime.now()
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error during manual scan: {e}")
            return {}
    
    def get_current_signals(self) -> Dict[str, Any]:
        """Get the most recent scan results"""
        return self.current_signals.copy()
    
    def get_signal_for_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get signal information for a specific symbol"""
        return self.current_signals.get(symbol)
    
    def add_symbol_to_watchlist(self, symbol: str):
        """Add a symbol to the watchlist"""
        symbol = symbol.upper()
        if symbol not in self.watchlist:
            self.watchlist.append(symbol)
            self.logger.info(f"Added {symbol} to watchlist")
    
    def remove_symbol_from_watchlist(self, symbol: str):
        """Remove a symbol from the watchlist"""
        symbol = symbol.upper()
        if symbol in self.watchlist:
            self.watchlist.remove(symbol)
            self.logger.info(f"Removed {symbol} from watchlist")
    
    def get_watchlist(self) -> List[str]:
        """Get current watchlist"""
        return self.watchlist.copy()
    
    def update_watchlist(self, new_watchlist: List[str]):
        """Update the entire watchlist"""
        self.watchlist = [symbol.upper() for symbol in new_watchlist]
        self.logger.info(f"Watchlist updated with {len(self.watchlist)} symbols")
    
    def get_scanner_status(self) -> Dict[str, Any]:
        """Get current scanner status"""
        return {
            'is_running': self.is_running,
            'last_scan_time': self.last_scan_time,
            'watchlist_size': len(self.watchlist),
            'current_signals_count': len(self.current_signals),
            'scan_interval_minutes': self.scan_interval,
            'market_hours_only': self.market_hours_only,
            'alpaca_connected': self.alpaca_client.is_connected()
        }
    
    def get_detailed_signal_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get detailed signal information for a symbol including recent data"""
        if symbol not in self.current_signals:
            return None
        
        try:
            # Get recent data for the symbol
            df = self.alpaca_client.get_historical_data(symbol, timeframe='5Min', limit=50)
            if df is None:
                return self.current_signals[symbol]
            
            # Calculate indicators
            df_with_indicators = self.squeeze_detector.calculate_indicators(df)
            
            # Get signal info
            signal_info = self.squeeze_detector.get_current_signal(df_with_indicators)
            signal_info['symbol'] = symbol
            
            # Add recent price history
            recent_prices = df['Close'].tail(10).tolist()
            signal_info['recent_prices'] = recent_prices
            
            return signal_info
            
        except Exception as e:
            self.logger.error(f"Error getting detailed signal info for {symbol}: {e}")
            return self.current_signals.get(symbol)
