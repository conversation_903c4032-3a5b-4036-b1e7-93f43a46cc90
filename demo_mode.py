"""
Demo Mode for Enhanced TTM Squeeze Trading
Simulates market data and signals for testing without live API connection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
from typing import Dict, List, Any, Optional

class DemoAlpacaClient:
    """Demo Alpaca client that simulates market data"""
    
    def __init__(self):
        """Initialize demo client"""
        self.connected = True
        self.demo_prices = self._generate_demo_prices()
        
    def is_connected(self) -> bool:
        """Always return True for demo"""
        return True
    
    def get_account_info(self) -> Dict[str, Any]:
        """Return demo account info"""
        return {
            'buying_power': 100000.0,
            'cash': 50000.0,
            'portfolio_value': 100000.0,
            'equity': 100000.0,
            'day_trade_count': 0,
            'pattern_day_trader': False
        }
    
    def is_market_open(self) -> bool:
        """Return True during market hours (9:30 AM - 4:00 PM ET)"""
        now = datetime.now()
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        return market_open <= now <= market_close
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get simulated current price"""
        if symbol in self.demo_prices:
            base_price = self.demo_prices[symbol]
            # Add some random variation
            variation = random.uniform(-0.02, 0.02)  # ±2%
            return base_price * (1 + variation)
        return 100.0  # Default price
    
    def get_historical_data(self, symbol: str, timeframe: str = '1Day', limit: int = 100) -> Optional[pd.DataFrame]:
        """Generate simulated historical data with TTM Squeeze patterns"""
        if symbol not in self.demo_prices:
            return None
        
        # Generate dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=limit)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')[:limit]
        
        # Generate price data with squeeze patterns
        base_price = self.demo_prices[symbol]
        prices = self._generate_squeeze_pattern(base_price, limit)
        
        # Create OHLCV data
        df = pd.DataFrame(index=dates[:len(prices)])
        df['Close'] = prices
        df['Open'] = df['Close'].shift(1).fillna(df['Close'].iloc[0])
        
        # Generate High/Low with realistic spreads
        spread = df['Close'] * 0.01  # 1% spread
        df['High'] = df[['Open', 'Close']].max(axis=1) + spread * np.random.uniform(0, 1, len(df))
        df['Low'] = df[['Open', 'Close']].min(axis=1) - spread * np.random.uniform(0, 1, len(df))
        
        # Generate volume
        df['Volume'] = np.random.randint(100000, 2000000, len(df))
        
        return df
    
    def get_multiple_historical_data(self, symbols: List[str], timeframe: str = '1Day', limit: int = 100) -> Dict[str, pd.DataFrame]:
        """Get historical data for multiple symbols"""
        data_dict = {}
        for symbol in symbols:
            df = self.get_historical_data(symbol, timeframe, limit)
            if df is not None:
                data_dict[symbol] = df
        return data_dict
    
    def place_bracket_order(self, symbol: str, qty: int, side: str, stop_loss_price: float, take_profit_price: float) -> Optional[str]:
        """Simulate placing a bracket order"""
        order_id = f"DEMO_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"🎭 DEMO ORDER PLACED:")
        print(f"   Symbol: {symbol}")
        print(f"   Quantity: {qty}")
        print(f"   Side: {side}")
        print(f"   Stop Loss: ${stop_loss_price:.2f}")
        print(f"   Take Profit: ${take_profit_price:.2f}")
        print(f"   Order ID: {order_id}")
        return order_id
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """Return demo positions"""
        return [
            {
                'symbol': 'AAPL',
                'qty': 100,
                'side': 'long',
                'market_value': 15000.0,
                'cost_basis': 14500.0,
                'unrealized_pl': 500.0,
                'unrealized_plpc': 0.034,
                'avg_entry_price': 145.0,
                'current_price': 150.0
            }
        ]
    
    def _generate_demo_prices(self) -> Dict[str, float]:
        """Generate realistic demo prices for common stocks"""
        return {
            'AAPL': 150.0,
            'TSLA': 200.0,
            'MSFT': 300.0,
            'NVDA': 400.0,
            'GOOGL': 120.0,
            'AMZN': 130.0,
            'META': 250.0,
            'SPY': 420.0,
            'QQQ': 350.0,
            'IWM': 180.0
        }
    
    def _generate_squeeze_pattern(self, base_price: float, length: int) -> List[float]:
        """Generate price data with TTM Squeeze patterns"""
        prices = []
        current_price = base_price
        
        # Create different phases
        phase_length = length // 4
        
        for i in range(length):
            if i < phase_length:
                # Normal volatility phase
                change = np.random.normal(0, 0.02)
            elif i < phase_length * 2:
                # Building squeeze phase - reduced volatility
                change = np.random.normal(0, 0.005)
            elif i < phase_length * 3:
                # Tight squeeze phase - very low volatility
                change = np.random.normal(0, 0.002)
            else:
                # Breakout phase - increased volatility and trend
                trend = 0.01 if random.random() > 0.5 else -0.01
                change = np.random.normal(trend, 0.03)
            
            current_price *= (1 + change)
            prices.append(current_price)
        
        return prices

def create_demo_signals() -> Dict[str, Any]:
    """Create demo TTM Squeeze signals for testing"""
    symbols = ['AAPL', 'TSLA', 'MSFT', 'NVDA', 'GOOGL']
    signals = {}
    
    for i, symbol in enumerate(symbols):
        # Create different types of signals
        if i == 0:  # Enhanced signal
            signals[symbol] = {
                'symbol': symbol,
                'current_price': 150.0 + random.uniform(-5, 5),
                'has_signal': True,
                'early_signal': True,
                'enhanced_early_signal': True,
                'squeeze_active': True,
                'squeeze_count': 8,
                'momentum': -0.0234,
                'momentum_strength': 0.0234,
                'red_phase': False,
                'yellow_phase': True,
                'squeeze_strength': 0.156,
                'atr': 2.34,
                'timestamp': datetime.now()
            }
        elif i == 1:  # Yellow phase
            signals[symbol] = {
                'symbol': symbol,
                'current_price': 200.0 + random.uniform(-10, 10),
                'has_signal': False,
                'early_signal': False,
                'enhanced_early_signal': False,
                'squeeze_active': True,
                'squeeze_count': 6,
                'momentum': -0.0156,
                'momentum_strength': 0.0156,
                'red_phase': False,
                'yellow_phase': True,
                'squeeze_strength': 0.234,
                'atr': 3.45,
                'timestamp': datetime.now()
            }
        elif i == 2:  # Red phase
            signals[symbol] = {
                'symbol': symbol,
                'current_price': 300.0 + random.uniform(-15, 15),
                'has_signal': False,
                'early_signal': False,
                'enhanced_early_signal': False,
                'squeeze_active': True,
                'squeeze_count': 4,
                'momentum': -0.0345,
                'momentum_strength': 0.0345,
                'red_phase': True,
                'yellow_phase': False,
                'squeeze_strength': 0.123,
                'atr': 4.56,
                'timestamp': datetime.now()
            }
    
    return signals

def enable_demo_mode():
    """Enable demo mode by replacing the Alpaca client"""
    print("🎭 ENABLING DEMO MODE")
    print("=" * 30)
    print("✅ Demo Alpaca client activated")
    print("✅ Simulated market data enabled")
    print("✅ Demo trading signals created")
    print("✅ All features available for testing")
    print("\n⚠️  Note: This is demo mode - no real trades will be placed!")
    
    return DemoAlpacaClient()

if __name__ == "__main__":
    # Test demo mode
    demo_client = enable_demo_mode()
    
    print(f"\n📊 Demo Account Info:")
    account = demo_client.get_account_info()
    for key, value in account.items():
        print(f"   {key}: {value}")
    
    print(f"\n📈 Demo Market Data:")
    df = demo_client.get_historical_data('AAPL', limit=10)
    if df is not None:
        print(f"   Retrieved {len(df)} bars for AAPL")
        print(f"   Latest close: ${df['Close'].iloc[-1]:.2f}")
    
    print(f"\n🎯 Demo Signals:")
    signals = create_demo_signals()
    for symbol, signal in signals.items():
        signal_type = "🚀 ENHANCED" if signal['enhanced_early_signal'] else "🟡 YELLOW" if signal['yellow_phase'] else "🔴 RED"
        print(f"   {symbol}: {signal_type} - ${signal['current_price']:.2f}")
