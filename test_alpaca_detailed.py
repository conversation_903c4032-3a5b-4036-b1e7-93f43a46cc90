"""
Detailed Alpaca API Testing
More comprehensive testing to diagnose the exact issue
"""

import requests
import json
import base64
from datetime import datetime

def test_alpaca_rest_api():
    """Test Alpaca API using direct REST calls"""
    print("🔍 DETAILED ALPACA API TESTING")
    print("=" * 40)
    
    # API credentials
    api_key = "PK5UKOC4050J3MSE546C"
    secret_key = "M4XzwehyTrRn4Reknwg8RWf1v4fW4GFI99WW5kwh"
    base_url = "https://paper-api.alpaca.markets"
    
    # Test 1: Basic authentication
    print("1. Testing basic authentication...")
    headers = {
        'APCA-API-KEY-ID': api_key,
        'APCA-API-SECRET-KEY': secret_key,
        'Content-Type': 'application/json'
    }
    
    try:
        # Test account endpoint
        response = requests.get(f"{base_url}/v2/account", headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            account_data = response.json()
            print("   ✅ Authentication successful!")
            print(f"   Account ID: {account_data.get('id', 'N/A')}")
            print(f"   Status: {account_data.get('status', 'N/A')}")
            print(f"   Buying Power: ${float(account_data.get('buying_power', 0)):,.2f}")
            return True
            
        elif response.status_code == 401:
            print("   ❌ 401 Unauthorized - Invalid API credentials")
            print("   The API keys are not valid or have been revoked")
            
        elif response.status_code == 403:
            print("   ❌ 403 Forbidden - Access denied")
            print("   The API keys may be for live trading, not paper trading")
            print("   Or the account may be restricted")
            
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("   ❌ Request timeout - Check internet connection")
        
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection error - Check internet connection")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return False

def test_live_vs_paper():
    """Test if keys work with live API instead of paper"""
    print("\n2. Testing live API endpoint...")
    
    api_key = "PK5UKOC4050J3MSE546C"
    secret_key = "M4XzwehyTrRn4Reknwg8RWf1v4fW4GFI99WW5kwh"
    live_url = "https://api.alpaca.markets"  # Live trading URL
    
    headers = {
        'APCA-API-KEY-ID': api_key,
        'APCA-API-SECRET-KEY': secret_key,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{live_url}/v2/account", headers=headers, timeout=10)
        print(f"   Live API Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("   ⚠️  Keys work with LIVE trading API!")
            print("   You may need to use live URL instead of paper URL")
            return True
        else:
            print("   Keys don't work with live API either")
            
    except Exception as e:
        print(f"   Error testing live API: {e}")
    
    return False

def test_alpaca_python_lib():
    """Test with different alpaca library configurations"""
    print("\n3. Testing alpaca-trade-api library configurations...")
    
    try:
        import alpaca_trade_api as tradeapi
        
        api_key = "PK5UKOC4050J3MSE546C"
        secret_key = "M4XzwehyTrRn4Reknwg8RWf1v4fW4GFI99WW5kwh"
        
        # Test different configurations
        configs = [
            {"base_url": "https://paper-api.alpaca.markets", "name": "Paper Trading"},
            {"base_url": "https://api.alpaca.markets", "name": "Live Trading"},
        ]
        
        for config in configs:
            print(f"\n   Testing {config['name']}...")
            try:
                api = tradeapi.REST(
                    key_id=api_key,
                    secret_key=secret_key,
                    base_url=config["base_url"],
                    api_version='v2'
                )
                
                account = api.get_account()
                print(f"   ✅ {config['name']} - SUCCESS!")
                print(f"      Account Status: {account.status}")
                print(f"      Account ID: {account.id}")
                return True
                
            except Exception as e:
                print(f"   ❌ {config['name']} - FAILED: {e}")
                
    except ImportError:
        print("   ❌ alpaca-trade-api not installed")
    
    return False

def check_api_key_format():
    """Check if API key format is correct"""
    print("\n4. Checking API key format...")
    
    api_key = "PK5UKOC4050J3MSE546C"
    secret_key = "M4XzwehyTrRn4Reknwg8RWf1v4fW4GFI99WW5kwh"
    
    print(f"   API Key: {api_key}")
    print(f"   API Key Length: {len(api_key)}")
    print(f"   API Key Format: {'✅ Correct' if api_key.startswith('PK') and len(api_key) == 20 else '❌ Incorrect'}")
    
    print(f"   Secret Key: {secret_key[:10]}...")
    print(f"   Secret Key Length: {len(secret_key)}")
    print(f"   Secret Key Format: {'✅ Correct' if len(secret_key) == 40 else '❌ Incorrect'}")

def main():
    """Run all tests"""
    print("COMPREHENSIVE ALPACA API DIAGNOSTIC")
    print("=" * 50)
    
    # Check API key format
    check_api_key_format()
    
    # Test REST API directly
    rest_success = test_alpaca_rest_api()
    
    if not rest_success:
        # Test live vs paper
        live_success = test_live_vs_paper()
        
        # Test Python library
        lib_success = test_alpaca_python_lib()
        
        if not any([rest_success, live_success, lib_success]):
            print("\n❌ ALL TESTS FAILED")
            print("\n🔧 RECOMMENDATIONS:")
            print("1. Generate new API keys at https://app.alpaca.markets/paper/dashboard/overview")
            print("2. Ensure you're generating PAPER trading keys, not live keys")
            print("3. Copy the keys exactly as shown (no extra spaces)")
            print("4. Make sure your Alpaca account is active and verified")
        else:
            print("\n✅ SOME TESTS PASSED - Check the working configuration above")
    else:
        print("\n🎉 API CONNECTION SUCCESSFUL!")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
