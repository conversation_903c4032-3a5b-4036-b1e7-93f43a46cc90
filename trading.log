2025-06-09 19:38:13,717 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:38:13,916 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:38:13,932 - trade_manager - INFO - Created trade log file: trades.csv
2025-06-09 19:38:13,933 - trade_manager - INFO - Trade manager initialized
2025-06-09 19:38:14,149 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:38:14,151 - scanner - INFO - Scanner initialized with 42 symbols
2025-06-09 19:43:11,003 - __main__ - INFO - ==================================================
2025-06-09 19:43:11,003 - __main__ - INFO - TTM Squeeze Trading Application Starting
2025-06-09 19:43:11,003 - __main__ - INFO - Timestamp: 2025-06-09 19:43:11.003579
2025-06-09 19:43:11,003 - __main__ - INFO - ==================================================
2025-06-09 19:43:11,004 - __main__ - INFO - Loaded watchlist with 42 symbols
2025-06-09 19:43:11,004 - __main__ - INFO - All checks passed - starting application
2025-06-09 19:43:11,166 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:43:11,167 - scanner - INFO - Scanner initialized with 42 symbols
2025-06-09 19:43:11,362 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:43:11,363 - trade_manager - INFO - Trade manager initialized
2025-06-09 19:43:11,504 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:45:00,678 - __main__ - INFO - ==================================================
2025-06-09 19:45:00,678 - __main__ - INFO - TTM Squeeze Trading Application Starting
2025-06-09 19:45:00,678 - __main__ - INFO - Timestamp: 2025-06-09 19:45:00.678881
2025-06-09 19:45:00,678 - __main__ - INFO - ==================================================
2025-06-09 19:45:00,679 - __main__ - INFO - Loaded watchlist with 42 symbols
2025-06-09 19:45:00,679 - __main__ - INFO - All checks passed - starting application
2025-06-09 19:45:01,659 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:45:01,661 - scanner - INFO - Scanner initialized with 42 symbols
2025-06-09 19:45:01,860 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:45:01,861 - trade_manager - INFO - Trade manager initialized
2025-06-09 19:45:02,003 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:45:02,111 - gui.main_window - INFO - Main window initialized
2025-06-09 19:46:05,802 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:46:05,949 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:46:05,949 - trade_manager - INFO - Trade manager initialized
2025-06-09 19:46:06,093 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:46:06,095 - scanner - INFO - Scanner initialized with 42 symbols
2025-06-09 19:46:06,151 - alpaca_client - ERROR - Error getting account info: 'Account' object has no attribute 'day_trade_count'
2025-06-09 19:46:06,306 - scanner - INFO - Performing manual scan...
2025-06-09 19:46:06,458 - alpaca_client - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-05-10T19:46:06.307093' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:06.307093": extra text: "T19:46:06.307093"
2025-06-09 19:46:06,458 - alpaca_client - WARNING - No data available for AAPL
2025-06-09 19:46:06,610 - alpaca_client - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-05-10T19:46:06.559335' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:06.559335": extra text: "T19:46:06.559335"
2025-06-09 19:46:06,612 - alpaca_client - WARNING - No data available for MSFT
2025-06-09 19:46:06,766 - alpaca_client - ERROR - Error getting historical data for GOOGL: Invalid format for parameter start: error parsing '2025-05-10T19:46:06.713257' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:06.713257": extra text: "T19:46:06.713257"
2025-06-09 19:46:06,768 - alpaca_client - WARNING - No data available for GOOGL
2025-06-09 19:46:06,923 - alpaca_client - ERROR - Error getting historical data for AMZN: Invalid format for parameter start: error parsing '2025-05-10T19:46:06.870537' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:06.870537": extra text: "T19:46:06.870537"
2025-06-09 19:46:06,924 - alpaca_client - WARNING - No data available for AMZN
2025-06-09 19:46:07,076 - alpaca_client - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.026185' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.026185": extra text: "T19:46:07.026185"
2025-06-09 19:46:07,076 - alpaca_client - WARNING - No data available for TSLA
2025-06-09 19:46:07,228 - alpaca_client - ERROR - Error getting historical data for META: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.177765' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.177765": extra text: "T19:46:07.177765"
2025-06-09 19:46:07,229 - alpaca_client - WARNING - No data available for META
2025-06-09 19:46:07,382 - alpaca_client - ERROR - Error getting historical data for NVDA: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.330396' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.330396": extra text: "T19:46:07.330396"
2025-06-09 19:46:07,383 - alpaca_client - WARNING - No data available for NVDA
2025-06-09 19:46:07,534 - alpaca_client - ERROR - Error getting historical data for NFLX: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.485041' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.485041": extra text: "T19:46:07.485041"
2025-06-09 19:46:07,536 - alpaca_client - WARNING - No data available for NFLX
2025-06-09 19:46:07,689 - alpaca_client - ERROR - Error getting historical data for JPM: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.637642' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.637642": extra text: "T19:46:07.637642"
2025-06-09 19:46:07,691 - alpaca_client - WARNING - No data available for JPM
2025-06-09 19:46:07,844 - alpaca_client - ERROR - Error getting historical data for BAC: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.793225' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.793225": extra text: "T19:46:07.793225"
2025-06-09 19:46:07,846 - alpaca_client - WARNING - No data available for BAC
2025-06-09 19:46:08,011 - alpaca_client - ERROR - Error getting historical data for WFC: Invalid format for parameter start: error parsing '2025-05-10T19:46:07.947976' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:07.947976": extra text: "T19:46:07.947976"
2025-06-09 19:46:08,013 - alpaca_client - WARNING - No data available for WFC
2025-06-09 19:46:08,168 - alpaca_client - ERROR - Error getting historical data for GS: Invalid format for parameter start: error parsing '2025-05-10T19:46:08.115611' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:08.115611": extra text: "T19:46:08.115611"
2025-06-09 19:46:08,170 - alpaca_client - WARNING - No data available for GS
2025-06-09 19:46:08,318 - alpaca_client - ERROR - Error getting historical data for MS: Invalid format for parameter start: error parsing '2025-05-10T19:46:08.271724' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:08.271724": extra text: "T19:46:08.271724"
2025-06-09 19:46:08,319 - alpaca_client - WARNING - No data available for MS
2025-06-09 19:46:08,475 - alpaca_client - ERROR - Error getting historical data for JNJ: Invalid format for parameter start: error parsing '2025-05-10T19:46:08.420011' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:08.420011": extra text: "T19:46:08.420011"
2025-06-09 19:46:08,475 - alpaca_client - WARNING - No data available for JNJ
2025-06-09 19:46:08,626 - alpaca_client - ERROR - Error getting historical data for PFE: Invalid format for parameter start: error parsing '2025-05-10T19:46:08.576162' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:08.576162": extra text: "T19:46:08.576162"
2025-06-09 19:46:08,628 - alpaca_client - WARNING - No data available for PFE
2025-06-09 19:46:08,783 - alpaca_client - ERROR - Error getting historical data for UNH: Invalid format for parameter start: error parsing '2025-05-10T19:46:08.729946' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:08.729946": extra text: "T19:46:08.729946"
2025-06-09 19:46:08,785 - alpaca_client - WARNING - No data available for UNH
2025-06-09 19:46:08,935 - alpaca_client - ERROR - Error getting historical data for ABBV: Invalid format for parameter start: error parsing '2025-05-10T19:46:08.887157' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:08.887157": extra text: "T19:46:08.887157"
2025-06-09 19:46:08,936 - alpaca_client - WARNING - No data available for ABBV
2025-06-09 19:46:09,088 - alpaca_client - ERROR - Error getting historical data for MRK: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.038024' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.038024": extra text: "T19:46:09.038024"
2025-06-09 19:46:09,089 - alpaca_client - WARNING - No data available for MRK
2025-06-09 19:46:09,245 - alpaca_client - ERROR - Error getting historical data for KO: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.192077' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.192077": extra text: "T19:46:09.192077"
2025-06-09 19:46:09,247 - alpaca_client - WARNING - No data available for KO
2025-06-09 19:46:09,402 - alpaca_client - ERROR - Error getting historical data for PEP: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.348875' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.348875": extra text: "T19:46:09.348875"
2025-06-09 19:46:09,403 - alpaca_client - WARNING - No data available for PEP
2025-06-09 19:46:09,558 - alpaca_client - ERROR - Error getting historical data for WMT: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.504334' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.504334": extra text: "T19:46:09.504334"
2025-06-09 19:46:09,560 - alpaca_client - WARNING - No data available for WMT
2025-06-09 19:46:09,713 - alpaca_client - ERROR - Error getting historical data for HD: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.661672' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.661672": extra text: "T19:46:09.661672"
2025-06-09 19:46:09,715 - alpaca_client - WARNING - No data available for HD
2025-06-09 19:46:09,867 - alpaca_client - ERROR - Error getting historical data for DIS: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.817054' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.817054": extra text: "T19:46:09.817054"
2025-06-09 19:46:09,869 - alpaca_client - WARNING - No data available for DIS
2025-06-09 19:46:10,024 - alpaca_client - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-05-10T19:46:09.971335' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:09.971335": extra text: "T19:46:09.971335"
2025-06-09 19:46:10,026 - alpaca_client - WARNING - No data available for SPY
2025-06-09 19:46:10,179 - alpaca_client - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-05-10T19:46:10.128119' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:10.128119": extra text: "T19:46:10.128119"
2025-06-09 19:46:10,182 - alpaca_client - WARNING - No data available for QQQ
2025-06-09 19:46:10,337 - alpaca_client - ERROR - Error getting historical data for IWM: Invalid format for parameter start: error parsing '2025-05-10T19:46:10.283912' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:10.283912": extra text: "T19:46:10.283912"
2025-06-09 19:46:10,340 - alpaca_client - WARNING - No data available for IWM
2025-06-09 19:46:10,489 - alpaca_client - ERROR - Error getting historical data for XLF: Invalid format for parameter start: error parsing '2025-05-10T19:46:10.442524' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:10.442524": extra text: "T19:46:10.442524"
2025-06-09 19:46:10,490 - alpaca_client - WARNING - No data available for XLF
2025-06-09 19:46:10,640 - alpaca_client - ERROR - Error getting historical data for XLK: Invalid format for parameter start: error parsing '2025-05-10T19:46:10.591156' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:10.591156": extra text: "T19:46:10.591156"
2025-06-09 19:46:10,641 - alpaca_client - WARNING - No data available for XLK
2025-06-09 19:46:10,795 - alpaca_client - ERROR - Error getting historical data for XLE: Invalid format for parameter start: error parsing '2025-05-10T19:46:10.743192' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:10.743192": extra text: "T19:46:10.743192"
2025-06-09 19:46:10,797 - alpaca_client - WARNING - No data available for XLE
2025-06-09 19:46:10,952 - alpaca_client - ERROR - Error getting historical data for XLV: Invalid format for parameter start: error parsing '2025-05-10T19:46:10.899496' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:10.899496": extra text: "T19:46:10.899496"
2025-06-09 19:46:10,953 - alpaca_client - WARNING - No data available for XLV
2025-06-09 19:46:11,105 - alpaca_client - ERROR - Error getting historical data for XLI: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.055834' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.055834": extra text: "T19:46:11.055834"
2025-06-09 19:46:11,106 - alpaca_client - WARNING - No data available for XLI
2025-06-09 19:46:11,258 - alpaca_client - ERROR - Error getting historical data for XLU: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.207024' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.207024": extra text: "T19:46:11.207024"
2025-06-09 19:46:11,260 - alpaca_client - WARNING - No data available for XLU
2025-06-09 19:46:11,415 - alpaca_client - ERROR - Error getting historical data for XLP: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.362559' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.362559": extra text: "T19:46:11.362559"
2025-06-09 19:46:11,416 - alpaca_client - WARNING - No data available for XLP
2025-06-09 19:46:11,566 - alpaca_client - ERROR - Error getting historical data for SQ: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.517845' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.517845": extra text: "T19:46:11.517845"
2025-06-09 19:46:11,568 - alpaca_client - WARNING - No data available for SQ
2025-06-09 19:46:11,719 - alpaca_client - ERROR - Error getting historical data for ROKU: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.669611' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.669611": extra text: "T19:46:11.669611"
2025-06-09 19:46:11,720 - alpaca_client - WARNING - No data available for ROKU
2025-06-09 19:46:11,873 - alpaca_client - ERROR - Error getting historical data for ZOOM: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.822049' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.822049": extra text: "T19:46:11.822049"
2025-06-09 19:46:11,874 - alpaca_client - WARNING - No data available for ZOOM
2025-06-09 19:46:12,035 - alpaca_client - ERROR - Error getting historical data for DOCU: Invalid format for parameter start: error parsing '2025-05-10T19:46:11.975884' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:11.975884": extra text: "T19:46:11.975884"
2025-06-09 19:46:12,036 - alpaca_client - WARNING - No data available for DOCU
2025-06-09 19:46:12,187 - alpaca_client - ERROR - Error getting historical data for CRWD: Invalid format for parameter start: error parsing '2025-05-10T19:46:12.136941' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:12.136941": extra text: "T19:46:12.136941"
2025-06-09 19:46:12,190 - alpaca_client - WARNING - No data available for CRWD
2025-06-09 19:46:12,344 - alpaca_client - ERROR - Error getting historical data for GME: Invalid format for parameter start: error parsing '2025-05-10T19:46:12.292162' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:12.292162": extra text: "T19:46:12.292162"
2025-06-09 19:46:12,345 - alpaca_client - WARNING - No data available for GME
2025-06-09 19:46:12,492 - alpaca_client - ERROR - Error getting historical data for AMC: Invalid format for parameter start: error parsing '2025-05-10T19:46:12.446626' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:12.446626": extra text: "T19:46:12.446626"
2025-06-09 19:46:12,492 - alpaca_client - WARNING - No data available for AMC
2025-06-09 19:46:12,643 - alpaca_client - ERROR - Error getting historical data for BB: Invalid format for parameter start: error parsing '2025-05-10T19:46:12.592860' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:12.592860": extra text: "T19:46:12.592860"
2025-06-09 19:46:12,643 - alpaca_client - WARNING - No data available for BB
2025-06-09 19:46:12,798 - alpaca_client - ERROR - Error getting historical data for NOK: Invalid format for parameter start: error parsing '2025-05-10T19:46:12.745073' as RFC3339 or 2006-01-02 time: parsing time "2025-05-10T19:46:12.745073": extra text: "T19:46:12.745073"
2025-06-09 19:46:12,800 - alpaca_client - WARNING - No data available for NOK
2025-06-09 19:46:12,902 - alpaca_client - INFO - Retrieved data for 0 out of 42 symbols
2025-06-09 19:46:41,436 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:46:41,590 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:46:41,592 - trade_manager - INFO - Trade manager initialized
2025-06-09 19:46:41,738 - alpaca_client - INFO - Connected to Alpaca. Account status: ACTIVE
2025-06-09 19:46:41,740 - scanner - INFO - Scanner initialized with 42 symbols
2025-06-09 19:46:41,933 - scanner - INFO - Performing manual scan...
2025-06-09 19:46:48,159 - alpaca_client - WARNING - No data returned for SQ
2025-06-09 19:46:48,160 - alpaca_client - WARNING - No data available for SQ
2025-06-09 19:46:48,493 - alpaca_client - WARNING - No data returned for ZOOM
2025-06-09 19:46:48,494 - alpaca_client - WARNING - No data available for ZOOM
2025-06-09 19:46:49,657 - alpaca_client - INFO - Retrieved data for 40 out of 42 symbols
2025-06-09 19:48:56,763 - scanner - INFO - Market scanner started - scanning every 5 minutes
2025-06-09 19:49:36,789 - scanner - INFO - Market scanner stopped
2025-06-09 19:49:36,820 - __main__ - INFO - Application shutdown complete
2025-06-10 10:42:35,384 - __main__ - INFO - ==================================================
2025-06-10 10:42:35,384 - __main__ - INFO - TTM Squeeze Trading Application Starting
2025-06-10 10:42:35,384 - __main__ - INFO - Timestamp: 2025-06-10 10:42:35.384624
2025-06-10 10:42:35,384 - __main__ - INFO - ==================================================
2025-06-10 10:42:35,385 - __main__ - INFO - Loaded watchlist with 42 symbols
2025-06-10 10:42:35,385 - __main__ - INFO - All checks passed - starting application
2025-06-10 10:42:35,527 - alpaca_client - ERROR - Failed to initialize Alpaca client: forbidden.
2025-06-10 10:42:35,528 - scanner - INFO - Scanner initialized with 42 symbols
2025-06-10 10:42:35,663 - alpaca_client - ERROR - Failed to initialize Alpaca client: forbidden.
2025-06-10 10:42:35,664 - trade_manager - INFO - Trade manager initialized
2025-06-10 10:42:35,802 - alpaca_client - ERROR - Failed to initialize Alpaca client: forbidden.
2025-06-10 10:42:35,803 - trailing_stop_manager - INFO - Trailing Stop Manager initialized
2025-06-10 10:42:35,873 - gui.main_window - INFO - Main window initialized
2025-06-10 10:42:40,692 - scanner - ERROR - Cannot start scanner - Alpaca client not connected
2025-06-10 10:43:53,642 - scanner - INFO - Market scanner stopped
2025-06-10 10:43:53,669 - __main__ - INFO - Application shutdown complete
