"""
Test Alpaca Connection
Quick diagnostic script to test Alpaca API connection
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_alpaca_connection():
    """Test Alpaca connection and diagnose issues"""
    print("🔍 ALPACA CONNECTION DIAGNOSTIC")
    print("=" * 40)
    
    try:
        # Test 1: Import alpaca_trade_api
        print("1. Testing alpaca_trade_api import...")
        import alpaca_trade_api as tradeapi
        print("   ✅ alpaca_trade_api imported successfully")
        
        # Test 2: Load configuration
        print("\n2. Testing configuration...")
        from config import config
        
        api_key = config.get("alpaca.api_key")
        secret_key = config.get("alpaca.secret_key")
        base_url = config.get("alpaca.base_url")
        
        print(f"   API Key: {api_key[:10]}..." if api_key else "   ❌ No API Key")
        print(f"   Secret Key: {secret_key[:10]}..." if secret_key else "   ❌ No Secret Key")
        print(f"   Base URL: {base_url}")
        
        if not api_key or not secret_key:
            print("   ❌ Missing API credentials")
            return False
        
        # Test 3: Initialize API client
        print("\n3. Testing API client initialization...")
        api = tradeapi.REST(
            key_id=api_key,
            secret_key=secret_key,
            base_url=base_url,
            api_version='v2'
        )
        print("   ✅ API client initialized")
        
        # Test 4: Test connection with account info
        print("\n4. Testing connection with account info...")
        account = api.get_account()
        print(f"   ✅ Connected! Account status: {account.status}")
        print(f"   Account ID: {account.id}")
        print(f"   Buying Power: ${float(account.buying_power):,.2f}")
        print(f"   Portfolio Value: ${float(account.portfolio_value):,.2f}")
        
        # Test 5: Test market status
        print("\n5. Testing market status...")
        clock = api.get_clock()
        print(f"   Market Open: {clock.is_open}")
        print(f"   Next Open: {clock.next_open}")
        print(f"   Next Close: {clock.next_close}")
        
        # Test 6: Test data retrieval
        print("\n6. Testing data retrieval...")
        try:
            bars = api.get_bars('AAPL', '1Day', limit=5).df
            print(f"   ✅ Retrieved {len(bars)} bars for AAPL")
            print(f"   Latest close: ${bars['close'].iloc[-1]:.2f}")
        except Exception as e:
            print(f"   ⚠️  Data retrieval issue: {e}")
        
        print("\n🎉 ALPACA CONNECTION SUCCESSFUL!")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        print("   Try: pip install alpaca-trade-api")
        return False
        
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        
        # Common error diagnostics
        error_str = str(e).lower()
        if "unauthorized" in error_str or "forbidden" in error_str:
            print("\n🔧 DIAGNOSIS: Invalid API credentials")
            print("   - Check your API key and secret in config.json")
            print("   - Ensure you're using paper trading credentials")
            print("   - Verify credentials at https://app.alpaca.markets/paper/dashboard/overview")
            
        elif "network" in error_str or "connection" in error_str:
            print("\n🔧 DIAGNOSIS: Network connection issue")
            print("   - Check your internet connection")
            print("   - Try again in a few moments")
            
        elif "rate limit" in error_str:
            print("\n🔧 DIAGNOSIS: Rate limit exceeded")
            print("   - Wait a few minutes and try again")
            
        else:
            print(f"\n🔧 DIAGNOSIS: Unknown error")
            print(f"   Error details: {e}")
            
        return False

def fix_alpaca_client():
    """Try to fix common Alpaca client issues"""
    print("\n🔧 ATTEMPTING FIXES...")
    print("-" * 30)
    
    try:
        # Fix 1: Update alpaca client
        from alpaca_client import AlpacaClient
        
        print("1. Creating new AlpacaClient instance...")
        client = AlpacaClient()
        
        if client.is_connected():
            print("   ✅ AlpacaClient connected successfully!")
            
            # Test account info
            account_info = client.get_account_info()
            if account_info:
                print(f"   Account info retrieved: ${account_info['portfolio_value']:,.2f}")
            
            # Test market status
            market_open = client.is_market_open()
            print(f"   Market status: {'Open' if market_open else 'Closed'}")
            
            return True
        else:
            print("   ❌ AlpacaClient still not connected")
            return False
            
    except Exception as e:
        print(f"   ❌ Fix attempt failed: {e}")
        return False

if __name__ == "__main__":
    print("ALPACA CONNECTION DIAGNOSTIC TOOL")
    print("=" * 50)
    
    # Run connection test
    success = test_alpaca_connection()
    
    if not success:
        # Try fixes
        fix_success = fix_alpaca_client()
        
        if fix_success:
            print("\n🎉 CONNECTION FIXED!")
        else:
            print("\n❌ CONNECTION STILL FAILED")
            print("\nNext steps:")
            print("1. Verify your Alpaca API credentials")
            print("2. Check your internet connection")
            print("3. Try restarting the application")
    
    input("\nPress Enter to exit...")
