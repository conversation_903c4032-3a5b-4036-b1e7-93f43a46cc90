"""
TTM Squeeze Trading Application
Main entry point for the desktop trading application
"""
import sys
import os
import logging
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import config
from gui.main_window import MainWindow

def setup_logging():
    """Setup application logging"""
    log_level = getattr(logging, config.get("logging.log_level", "INFO").upper())
    log_file = config.get("logging.log_file", "trading.log")
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else "logs"
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("TTM Squeeze Trading Application Starting")
    logger.info(f"Timestamp: {datetime.now()}")
    logger.info("=" * 50)

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import alpaca_trade_api
    except ImportError:
        missing_deps.append("alpaca-trade-api")
    
    try:
        import schedule
    except ImportError:
        missing_deps.append("schedule")
    
    if missing_deps:
        error_msg = (
            "Missing required dependencies:\n\n" +
            "\n".join(f"- {dep}" for dep in missing_deps) +
            "\n\nPlease install them using:\n" +
            f"pip install {' '.join(missing_deps)}"
        )
        
        # Try to show GUI error if tkinter is available
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Missing Dependencies", error_msg)
            root.destroy()
        except:
            print(f"ERROR: {error_msg}")
        
        return False
    
    return True

def check_configuration():
    """Check if application is properly configured"""
    logger = logging.getLogger(__name__)
    
    # Check Alpaca credentials
    if not config.validate_alpaca_credentials():
        error_msg = (
            "Alpaca API credentials not configured!\n\n"
            "Please update config.json with your Alpaca API credentials:\n"
            "- api_key\n"
            "- secret_key\n\n"
            "You can get these from your Alpaca account dashboard."
        )
        
        logger.error("Alpaca credentials not configured")
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Configuration Error", error_msg)
            root.destroy()
        except:
            print(f"ERROR: {error_msg}")
        
        return False
    
    # Check watchlist
    watchlist = config.load_watchlist()
    if not watchlist:
        logger.warning("No symbols in watchlist - using default symbols")
        # Could add default symbols here if needed
    else:
        logger.info(f"Loaded watchlist with {len(watchlist)} symbols")
    
    return True

def main():
    """Main application entry point"""
    try:
        # Setup logging first
        setup_logging()
        logger = logging.getLogger(__name__)
        
        # Check dependencies
        if not check_dependencies():
            logger.error("Dependency check failed")
            return 1
        
        # Check configuration
        if not check_configuration():
            logger.error("Configuration check failed")
            return 1
        
        logger.info("All checks passed - starting application")
        
        # Create and run main window
        app = MainWindow()
        app.run()
        
        logger.info("Application shutdown complete")
        return 0
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        
        # Try to show error dialog
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Application Error", f"An unexpected error occurred:\n\n{e}")
            root.destroy()
        except:
            pass
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
