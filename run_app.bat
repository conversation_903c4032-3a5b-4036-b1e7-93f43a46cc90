@echo off
echo TTM Squeeze Trading Application
echo ================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install dependencies if requirements.txt exists
if exist requirements.txt (
    echo Installing/updating dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo Starting TTM Squeeze Trading Application...
echo.

REM Run the application
python main.py

echo.
echo Application closed.
pause
