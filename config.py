"""
Configuration management for TTM Squeeze Trading Application
"""
import json
import os
from typing import Dict, Any, List
import logging

class Config:
    """Configuration manager for the trading application"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self._setup_logging()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            else:
                logging.warning(f"Config file {self.config_file} not found, using defaults")
                return self._get_default_config()
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Return default configuration"""
        return {
            "alpaca": {
                "api_key": "",
                "secret_key": "",
                "base_url": "https://paper-api.alpaca.markets",
                "data_url": "https://data.alpaca.markets"
            },
            "trading": {
                "position_size_percent": 2.0,
                "max_concurrent_trades": 5,
                "stop_loss_percent": 2.0,
                "take_profit_ratio": 2.0,
                "risk_per_trade_percent": 1.0
            },
            "scanning": {
                "scan_interval_minutes": 5,
                "market_hours_only": True,
                "premarket_scan": False,
                "afterhours_scan": False
            },
            "ttm_squeeze": {
                "bb_length": 20,
                "bb_multiplier": 2.0,
                "kc_length": 20,
                "kc_multiplier": 1.5,
                "min_squeeze_bars": 6,
                "momentum_length": 20
            },
            "gui": {
                "refresh_interval_seconds": 30,
                "max_opportunities_display": 50,
                "auto_scroll": True
            },
            "logging": {
                "log_level": "INFO",
                "log_file": "trading.log",
                "trade_log_file": "trades.csv",
                "backup_logs": True
            }
        }
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config["logging"]["log_level"].upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config["logging"]["log_file"]),
                logging.StreamHandler()
            ]
        )
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'alpaca.api_key')"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config_ref = self.config
        
        for key in keys[:-1]:
            if key not in config_ref:
                config_ref[key] = {}
            config_ref = config_ref[key]
        
        config_ref[keys[-1]] = value
    
    def save(self):
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logging.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            logging.error(f"Error saving config: {e}")
    
    def load_watchlist(self, watchlist_file: str = "watchlist.txt") -> List[str]:
        """Load stock symbols from watchlist file"""
        symbols = []
        try:
            if os.path.exists(watchlist_file):
                with open(watchlist_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            symbols.append(line.upper())
            else:
                logging.warning(f"Watchlist file {watchlist_file} not found")
        except Exception as e:
            logging.error(f"Error loading watchlist: {e}")
        
        return symbols
    
    def validate_alpaca_credentials(self) -> bool:
        """Check if Alpaca API credentials are configured"""
        api_key = self.get("alpaca.api_key")
        secret_key = self.get("alpaca.secret_key")
        return bool(api_key and secret_key)

# Global configuration instance
config = Config()
