"""
Demo script to show trading functionality
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from alpaca_client import AlpacaClient
from trade_manager import TradeManager
from scanner import MarketScanner
import time

def demo_trading_interface():
    """Demonstrate the trading interface capabilities"""
    print("=" * 60)
    print("TTM SQUEEZE TRADING APPLICATION - TRADING DEMO")
    print("=" * 60)
    
    # Initialize components
    print("Initializing trading components...")
    alpaca_client = AlpacaClient()
    trade_manager = TradeManager()
    scanner = MarketScanner()
    
    if not alpaca_client.is_connected():
        print("❌ ERROR: Alpaca client not connected!")
        return
    
    print("✅ All components initialized successfully!")
    print()
    
    # Show account info
    print("📊 ACCOUNT INFORMATION:")
    print("-" * 30)
    account_info = alpaca_client.get_account_info()
    if account_info:
        print(f"💰 Buying Power: ${account_info['buying_power']:,.2f}")
        print(f"💵 Cash: ${account_info['cash']:,.2f}")
        print(f"📈 Portfolio Value: ${account_info['portfolio_value']:,.2f}")
        print(f"🏦 Equity: ${account_info['equity']:,.2f}")
        print(f"📊 Day Trade Count: {account_info['day_trade_count']}")
        print(f"🔄 Pattern Day Trader: {account_info['pattern_day_trader']}")
    print()
    
    # Show market status
    print("🏪 MARKET STATUS:")
    print("-" * 20)
    market_open = alpaca_client.is_market_open()
    print(f"Market is: {'🟢 OPEN' if market_open else '🔴 CLOSED'}")
    print()
    
    # Show current positions
    print("📋 CURRENT POSITIONS:")
    print("-" * 25)
    positions = alpaca_client.get_positions()
    if positions:
        for pos in positions:
            pnl_color = "🟢" if pos['unrealized_pl'] >= 0 else "🔴"
            print(f"{pos['symbol']}: {pos['qty']} shares @ ${pos['avg_entry_price']:.2f}")
            print(f"  Current: ${pos['current_price']:.2f} | P&L: {pnl_color}${pos['unrealized_pl']:.2f} ({pos['unrealized_plpc']*100:.1f}%)")
    else:
        print("No current positions")
    print()
    
    # Show pending orders
    print("📝 PENDING ORDERS:")
    print("-" * 20)
    orders = alpaca_client.get_orders(status='open')
    if orders:
        for order in orders:
            print(f"{order['symbol']}: {order['side'].upper()} {order['qty']} @ {order['type'].upper()}")
            if order['limit_price']:
                print(f"  Limit: ${order['limit_price']:.2f}")
            if order['stop_price']:
                print(f"  Stop: ${order['stop_price']:.2f}")
    else:
        print("No pending orders")
    print()
    
    # Perform a quick scan
    print("🔍 PERFORMING MARKET SCAN:")
    print("-" * 30)
    print("Scanning for TTM Squeeze opportunities...")
    
    signals = scanner.manual_scan()
    
    if signals:
        print(f"Found {len(signals)} opportunities:")
        for symbol, signal in signals.items():
            signal_type = "🎯 SIGNAL" if signal['has_signal'] else "⏳ SQUEEZE"
            print(f"{signal_type} {symbol}: ${signal['current_price']:.2f}")
            print(f"  Squeeze: {signal['squeeze_count']} bars | Momentum: {signal['momentum']:.4f}")
            print(f"  Strength: {signal['squeeze_strength']:.3f}")
    else:
        print("No TTM Squeeze opportunities found at this time")
    print()
    
    # Show trading capabilities
    print("🚀 TRADING CAPABILITIES:")
    print("-" * 30)
    print("✅ Real-time market data from Alpaca")
    print("✅ TTM Squeeze pattern detection")
    print("✅ Automated bracket order placement")
    print("✅ Position sizing and risk management")
    print("✅ Stop-loss and take-profit automation")
    print("✅ Real-time P&L tracking")
    print("✅ Comprehensive trade logging")
    print("✅ Desktop GUI interface")
    print()
    
    print("🎯 HOW TO TRADE:")
    print("-" * 20)
    print("1. Launch the GUI: python main.py")
    print("2. Go to 'Market Scanner' tab")
    print("3. Click 'Start Scanner' for continuous monitoring")
    print("4. Select a symbol with 'SIGNAL' status")
    print("5. Click 'PLACE TRADE' for TTM Squeeze trades")
    print("6. Or click 'QUICK BUY' for immediate market orders")
    print("7. Monitor positions in 'Positions' tab")
    print("8. Trades are automatically managed with stop-loss/take-profit")
    print()
    
    print("⚠️  IMPORTANT NOTES:")
    print("-" * 25)
    print("• Currently using PAPER TRADING (safe for testing)")
    print("• Default position size: 2% of account equity")
    print("• Default stop-loss: 2% below entry")
    print("• Default take-profit: 2:1 risk/reward ratio")
    print("• Maximum 5 concurrent positions")
    print("• All trades logged to trades.csv")
    print()
    
    print("🎉 READY TO TRADE!")
    print("Launch the GUI with: python main.py")
    print("=" * 60)

if __name__ == "__main__":
    demo_trading_interface()
